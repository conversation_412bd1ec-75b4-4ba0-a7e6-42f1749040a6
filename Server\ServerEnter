local ReplicatedStorage 	= game:GetService("ReplicatedStorage")
local Players 				= game:GetService("Players")
local notifyManager 		= require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local protocolManager 		= require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local configManager			= require(ReplicatedStorage.Scripts.Share.Services.ConfigManager)
local eventsManager			= require(ReplicatedStorage.Scripts.Server.Manager.EventsManager)

local TrainEntity 			= require(ReplicatedStorage.Scripts.Server.Manager.TrainManager.TrainEntity)
local TrackEntity 			= require(ReplicatedStorage.Scripts.Server.Manager.TracksEntity)
local Till 					= require(ReplicatedStorage.Scripts.ItemInteraction.TillManager)
local Sell					= require(ReplicatedStorage.Scripts.ItemInteraction.SellManager)

local SunManager			= require(ReplicatedStorage.Scripts.Server.Manager.SunManager)
local TrainManager			=require(ReplicatedStorage.Scripts.Server.Manager.TrainManager)

local WeaponManager 		= require(ReplicatedStorage.Scripts.Server.Manager.WeaponManager)
local DeathBoxService 		= require(ReplicatedStorage.Scripts.Server.Services.DeathBoxService)
local AmmoInventoryServer 	= require(ReplicatedStorage.Scripts.Server.Services.AmmoInventoryServer)
local PlayerDownedService 	= require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
local BandageService 		= require(ReplicatedStorage.Scripts.Server.Services.BandageService)
local NewPlayerBoxService 	= require(ReplicatedStorage.Scripts.Server.Services.NewPlayerBoxService)
local TeleportExecutorService = require(ReplicatedStorage.Scripts.Server.Services.TeleportExecutorService)
local GameEndTeleportCommands = require(ReplicatedStorage.Scripts.Server.Commands.GameEndTeleportCommands)

--local ItemInteractionSystem = require(ReplicatedStorage.Scripts.ItemInteraction["ItemInteractionSystem "])
local CreatObjAndGUID 		= require(ReplicatedStorage.Scripts.ItemInteraction.CreatObjAndGUId)
local MonsetrSpawner		= require(ReplicatedStorage.Scripts.Monster.MonsterSpawner)
local GuardSpawner			= require(ReplicatedStorage.Scripts.Guard.GuardSpawner)
local mapManager			=require(ReplicatedStorage.Scripts.Server.Manager.MapManager)
local WaveMonstersManager	=require(ReplicatedStorage.Scripts.Server.Manager.WaveMonstersManager)
local EndManager			=require(ReplicatedStorage.Scripts.Server.Manager.EndManager)
local ServerEnter = {}

function ServerEnter.Init()	
	print("服务端初始化开始")
	notifyManager.GenerateRemote()
	protocolManager.GenerateRemote()
	-- 优先初始化事件管理器
	mapManager:Init()
	-- 禁止所有玩家自动重生
	ServerEnter:DisableAutoRespawn()
	--ItemInteractionSystem.initServer()
	configManager.Init()
	eventsManager:Init()
	-- 初始化武器管理
	WeaponManager.Init()
	-- 初始化死亡盒子服务
	DeathBoxService:Initialize()
	-- 初始化弹药库服务
	AmmoInventoryServer:Initialize()	
	-- 初始化玩家倒地服务
	PlayerDownedService:Initialize()
	-- 初始化绷带
	BandageService:Initialize()
	-- 初始化新手盒子服务
	NewPlayerBoxService:Initialize()

	-- 初始化传送执行服务
	TeleportExecutorService:Initialize()

	-- 初始化游戏结束传送命令系统
	GameEndTeleportCommands:Initialize()

	-- 初始化游戏实体
	TrainEntity.Init()
	TrackEntity.Init()	
	Till.InitSever()
	Sell.InitServer()
	CreatObjAndGUID.InitSever()
	MonsetrSpawner.InitServer()
	--GuardSpawner.InitServer()
	ServerEnter:SetupTestCommands() -- 添加测试命令	
	print("服务端初始化完成")
end

-- 🚫 系统重生功能已完全禁用
-- 注释：根据用户要求，已删除所有重生拦截和修改功能
-- 现在使用Roblox默认的重生机制

-- 🚫 重生拦截功能已删除
-- 注释：此功能已被完全禁用，使用Roblox默认重生机制

-- 🚫 自定义角色创建功能已删除
-- 注释：此功能已被完全禁用，使用Roblox默认角色创建机制

-- 设置测试命令
function ServerEnter:SetupTestCommands()
	warn("PlayerDownedService 加载结果:", PlayerDownedService) -- 确认模块加载

	-- 为已连接的玩家设置聊天命令
	for _, player in ipairs(Players:GetPlayers()) do
		ServerEnter:SetupPlayerChatCommands(player)
	end

	-- 为新加入的玩家设置聊天命令
	Players.PlayerAdded:Connect(function(player)
		ServerEnter:SetupPlayerChatCommands(player)
	end)

	-- 🚫 重生按钮濒死处理已禁用
	-- 注释：根据用户要求，已删除重生按钮拦截功能

	-- 但保留自我复活功能
	ServerEnter:SetupSelfReviveHandler()

	print("已设置测试命令 (/downed, /revive, /kill, /status)")
end

-- 🔄 设置自我复活处理器（已移至PlayerDownedService统一管理）
function ServerEnter:SetupSelfReviveHandler()
	print("🔄 自我复活处理器已在PlayerDownedService中统一管理")
	print("✅ 自我复活处理器设置完成")
end

-- 为单个玩家设置聊天命令
function ServerEnter:SetupPlayerChatCommands(player)
	warn("为玩家 " .. player.Name .. " 设置聊天命令")

	player.Chatted:Connect(function(message)
		warn("玩家 " .. player.Name .. " 发送了消息: " .. message)

		-- 测试命令：使自己进入濒死状态
		if message == "/downed" then
			local character = player.Character or player.CharacterAdded:Wait()
			local humanoid = character:FindFirstChildOfClass("Humanoid")
			if humanoid then
				-- 设置血量为0，触发濒死机制
				humanoid.Health = 0
				print("服务端: 已设置玩家 " .. player.Name .. " 进入濒死状态")
			end
		end

		-- 测试命令：恢复自己
		if message == "/revive" then
			PlayerDownedService:RevivePlayer(player)
			print("服务端: 已恢复玩家 " .. player.Name .. " 的状态")
		end

		-- 测试命令：杀死自己（绕过濒死状态）
		if message == "/kill" then
			local character = player.Character or player.CharacterAdded:Wait()
			local humanoid = character:FindFirstChildOfClass("Humanoid")
			if humanoid then
				-- 使用新的杀死玩家方法
				PlayerDownedService:KillPlayer(player)
				print("服务端: 已杀死玩家 " .. player.Name)
			end
		end

		-- 测试命令：显示所有玩家濒死状态
		if message == "/status" then
			local playersList = Players:GetPlayers()
			local message = "玩家状态列表:\n"

			for _, p in ipairs(playersList) do
				local isDowned = PlayerDownedService.DownedPlayers[p.UserId] == true
				local status = isDowned and "濒死" or "正常"
				message = message .. p.Name .. ": " .. status .. "\n"
			end

			-- 向命令发起者显示状态消息
			notifyManager.FireClient("ShowMessage", player, {
				message = message,
				duration = 5
			})

			print("服务端: 已显示玩家状态列表")
		end
	end)
end

return ServerEnter