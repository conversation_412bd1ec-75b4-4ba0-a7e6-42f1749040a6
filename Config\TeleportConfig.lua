--!strict
-- 传送配置文件
-- 在这里配置传送相关的设置

local TeleportConfig = {}

-- 初始场景ID配置
-- 请将此ID修改为您的初始场景ID
TeleportConfig.INITIAL_PLACE_ID = 89908111666289

-- 传送倒计时配置
TeleportConfig.COUNTDOWN_TIME = 10 -- 游戏结束后的传送倒计时时间（秒）

-- 传送失败重试配置
TeleportConfig.MAX_RETRY_ATTEMPTS = 3 -- 传送失败时的最大重试次数
TeleportConfig.RETRY_DELAY = 2 -- 重试间隔（秒）

-- 调试配置
TeleportConfig.DEBUG_MODE = true -- 是否启用调试输出

-- 获取初始场景ID
function TeleportConfig:GetInitialPlaceId()
	if self.DEBUG_MODE then
		print("📍 获取初始场景ID:", self.INITIAL_PLACE_ID)
	end
	return self.INITIAL_PLACE_ID
end

-- 获取倒计时时间
function TeleportConfig:GetCountdownTime()
	if self.DEBUG_MODE then
		print("⏰ 获取倒计时时间:", self.COUNTDOWN_TIME, "秒")
	end
	return self.COUNTDOWN_TIME
end

-- 验证配置
function TeleportConfig:ValidateConfig()
	local isValid = true
	local errors = {}
	
	-- 验证初始场景ID
	if not self.INITIAL_PLACE_ID or type(self.INITIAL_PLACE_ID) ~= "number" or self.INITIAL_PLACE_ID <= 0 then
		table.insert(errors, "INITIAL_PLACE_ID 必须是一个正整数")
		isValid = false
	end
	
	-- 验证倒计时时间
	if not self.COUNTDOWN_TIME or type(self.COUNTDOWN_TIME) ~= "number" or self.COUNTDOWN_TIME <= 0 then
		table.insert(errors, "COUNTDOWN_TIME 必须是一个正数")
		isValid = false
	end
	
	-- 验证重试配置
	if not self.MAX_RETRY_ATTEMPTS or type(self.MAX_RETRY_ATTEMPTS) ~= "number" or self.MAX_RETRY_ATTEMPTS < 0 then
		table.insert(errors, "MAX_RETRY_ATTEMPTS 必须是一个非负整数")
		isValid = false
	end
	
	if not self.RETRY_DELAY or type(self.RETRY_DELAY) ~= "number" or self.RETRY_DELAY < 0 then
		table.insert(errors, "RETRY_DELAY 必须是一个非负数")
		isValid = false
	end
	
	if not isValid then
		warn("❌ 传送配置验证失败:")
		for _, error in ipairs(errors) do
			warn("  - " .. error)
		end
	elseif self.DEBUG_MODE then
		print("✅ 传送配置验证通过")
	end
	
	return isValid, errors
end

-- 显示当前配置
function TeleportConfig:ShowConfig()
	print("📋 当前传送配置:")
	print("  初始场景ID:", self.INITIAL_PLACE_ID)
	print("  倒计时时间:", self.COUNTDOWN_TIME, "秒")
	print("  最大重试次数:", self.MAX_RETRY_ATTEMPTS)
	print("  重试延迟:", self.RETRY_DELAY, "秒")
	print("  调试模式:", self.DEBUG_MODE and "启用" or "禁用")
end

return TeleportConfig
