local ReplicatedStorage = game:GetService("ReplicatedStorage")
local StateConfig 		= require(ReplicatedStorage.Scripts.State.StateConfig) 
local Players 			= game:GetService("Players")

local StateManager = {}
StateManager.State ={
	Health = 100,
	MoveSpeed = 16,
	Attack = 15,
}
function StateManager.UpdateState(data)
	StateManager.State.Health = data.Health
	StateManager.State.MoveSpeed = data.MoveSpeed
	StateManager.State.Attack = data.Attack
end

function StateManager.AddState(player,Id)
	-- 确保 player 是有效的玩家实例
	if not player or not player:IsA("Player") then
		print("错误: 无效的玩家对象")
		return false
	end

	-- 获取玩家角色，确保角色已加载
	local character = player.Character
	if not character then
		character = player.CharacterAdded:Wait() -- 等待角色加载
	end

	-- 获取 Humanoid 组件，使用 WaitForChild 确保获取成功
	local humanoid = character:FindFirstChild("Humanoid") -- 最多等待5秒
	for _, config in ipairs(StateConfig) do
		if config.Id == Id then
			-- 计算切换前比例
			local multip = humanoid.Health / humanoid.MaxHealth
			StateManager.State.Health = StateManager.State.Health + config.Health
			StateManager.State.MoveSpeed = StateManager.State.MoveSpeed + config.MoveSpeed
			print("添加状态:", Id, "当前状态: 生命值=", StateManager.State.Health, "移动速度=", StateManager.State.MoveSpeed)
			humanoid.WalkSpeed = StateManager.State.MoveSpeed
			humanoid.MaxHealth = StateManager.State.Health
			humanoid.Health += humanoid.Health * multip
			return true 
		end
	end

	print("警告: 未找到状态配置 Id =", Id)
	return false
end
function StateManager.RemoveState(player,Id)
	-- 确保 player 是有效的玩家实例
	if not player or not player:IsA("Player") then
		print("错误: 无效的玩家对象")
		return false
	end

	-- 获取玩家角色，确保角色已加载
	local character = player.Character
	if not character then
		character = player.CharacterAdded:Wait() -- 等待角色加载
	end

	-- 获取 Humanoid 组件，使用 WaitForChild 确保获取成功
	local humanoid = character:FindFirstChild("Humanoid") -- 最多等待5秒
	for _, config in ipairs(StateConfig) do
		if config.Id == Id then
			-- 计算切换前比例
			StateManager.State.Health = StateManager.State.Health - config.Health
			StateManager.State.MoveSpeed = StateManager.State.MoveSpeed - config.MoveSpeed
			print("添加状态:", Id, "当前状态: 生命值=", StateManager.State.Health, "移动速度=", StateManager.State.MoveSpeed)
			humanoid.WalkSpeed = StateManager.State.MoveSpeed
			humanoid.MaxHealth = StateManager.State.Health
			return true 
		end
	end

	print("警告: 未找到状态配置 Id =", Id)
	return false
end
return StateManager
