# 战斗系统项目总览

## 📋 项目概述

本项目是一个完整的Roblox战斗系统，包含玩家濒死、复活、传送等核心功能。

## 🏗️ 系统架构

### 核心服务

#### 服务端服务
- **PlayerDownedService** - 玩家濒死状态管理
- **TeleportExecutorService** - 传送执行服务
- **TeleportDataManager** - 传送数据管理
- **NewPlayerBoxService** - 新手盒子服务
- **BandageService** - 绷带治疗服务

#### 客户端服务
- **PlayerDownedClient** - 客户端濒死UI和逻辑
- **NotifyService** - 客户端服务端通信

#### 共享服务
- **NotifyService** - 事件通信系统

### 配置系统
- **TeleportConfig** - 传送相关配置

## 🔄 核心功能流程

### 1. 玩家濒死流程
1. **玩家受伤** → 血量降至0
2. **进入濒死状态** → 显示濒死UI
3. **自我复活倒计时** → 10秒倒计时
4. **复活或游戏结束** → 根据情况处理

### 2. 游戏结束传送流程
1. **所有玩家濒死** → 触发游戏结束
2. **显示游戏结束UI** → 包含传送倒计时
3. **10秒倒计时** → 自动传送回初始场景
4. **服务端执行传送** → 安全的传送操作

### 3. 传送数据流程
1. **初始场景** → 收集玩家物品数据
2. **传送到战斗场景** → 携带玩家数据
3. **新手盒子生成** → 根据传送数据创建个性化盒子
4. **游戏结束返回** → 传送回初始场景

## 🧪 测试系统

### 可用测试模块
- **TeleportDataTest** - 传送数据测试
- **TeleportFixTest** - 传送修复验证
- **GameOverTeleportFlowTest** - 游戏结束流程测试
- **PlayerDownedFlowTest** - 濒死流程测试

### 管理员命令
```lua
/teleporttest [quick|full]  -- 传送功能测试
/flowtest                   -- 游戏流程测试
/alldown                    -- 模拟所有玩家濒死
/reviveall                  -- 复活所有玩家
/teleportnow                -- 立即传送回初始场景
/teleporthelp               -- 显示帮助信息
```

## ⚙️ 配置说明

### 传送配置 (TeleportConfig.lua)
```lua
-- 初始场景ID - 修改为您的初始场景ID
TeleportConfig.INITIAL_PLACE_ID = 89908111666289

-- 传送倒计时时间（秒）
TeleportConfig.COUNTDOWN_TIME = 10
```

## 🔧 技术实现

### 事件通信
- **客户端到服务端**: `NotifyService.FireServer()`
- **服务端到客户端**: `NotifyService.FireClient()`
- **服务端到所有客户端**: `NotifyService.FireAllClient()`

### 传送安全
- **客户端请求** → 发送传送请求
- **服务端验证** → 验证请求合法性
- **服务端执行** → 安全执行传送操作
- **错误处理** → 完整的错误处理机制

### 数据结构
```lua
-- 传送数据格式
{
    sourcePlace = 12345,
    timestamp = os.time(),
    playerItemData = {
        [userId] = {
            playerName = "玩家名字",
            occupationId = 4001,
            items = {物品数组}
        }
    }
}
```

## 📁 文件结构

```
Scripts/
├── Client/
│   └── Services/
│       └── PlayerDownedClient.lua
├── Server/
│   ├── Services/
│   │   ├── PlayerDownedService.lua
│   │   ├── TeleportExecutorService.lua
│   │   ├── TeleportDataManager.lua
│   │   ├── NewPlayerBoxService.lua
│   │   └── BandageService.lua
│   ├── Commands/
│   │   └── GameEndTeleportCommands.lua
│   └── ServerEnter.lua
├── Share/
│   └── Services/
│       └── NotifyService.lua
├── Config/
│   └── TeleportConfig.lua
├── Test/
│   ├── TeleportDataTest.lua
│   ├── TeleportFixTest.lua
│   ├── GameOverTeleportFlowTest.lua
│   ├── PlayerDownedFlowTest.lua
│   └── RunTests.server.lua
└── Documentation/
    ├── Project_Overview.md
    ├── TeleportDataUpdate_Summary.md
    └── PlayerDownedFlow_Fix_Summary.md
```

## 🚀 快速开始

### 1. 配置初始场景ID
编辑 `Scripts/Config/TeleportConfig.lua`：
```lua
TeleportConfig.INITIAL_PLACE_ID = 您的场景ID
```

### 2. 运行测试
在服务器控制台执行：
```lua
-- 运行所有测试
require(ReplicatedStorage.Scripts.Test.RunTests)

-- 快速测试传送功能
local test = require(ReplicatedStorage.Scripts.Test.TeleportFixTest)
test.runQuickTeleportTest()
```

### 3. 使用管理员命令
在游戏中输入：
```
/teleporthelp  -- 查看所有可用命令
/flowtest      -- 测试游戏流程
```

## 🔍 故障排除

### 常见问题
1. **传送失败** - 检查初始场景ID配置
2. **UI不显示** - 检查NotifyService事件注册
3. **测试失败** - 确保所有服务正确初始化

### 调试方法
1. 查看服务器控制台输出
2. 运行相应的测试脚本
3. 使用管理员命令进行调试

## 📝 维护说明

### 代码清理完成
- ✅ 移除重复的传送服务
- ✅ 整合测试脚本
- ✅ 简化配置管理
- ✅ 统一文档结构

### 系统优化
- ✅ 服务端安全传送
- ✅ 完整错误处理
- ✅ 统一事件系统
- ✅ 模块化架构

现在系统结构清晰，无冗余代码，功能完整且易于维护！
