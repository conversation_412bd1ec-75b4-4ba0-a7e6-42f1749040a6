local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local PlayerAttributeManager = require(ReplicatedStorage.Scripts.Server.Services.PlayerAttributeManager)

local PlayerDownedService = {}

-- 存储玩家濒死状态
PlayerDownedService.DownedPlayers = {}

-- 爬行相关属性（现在由客户端实现）
PlayerDownedService.CrawlSpeed = 8 -- 爬行速度

-- 存储原始属性
PlayerDownedService.OriginalProperties = {}

-- 初始化服务
function PlayerDownedService:Initialize()
	print("PlayerDownedService 初始化开始")

	-- 初始化属性管理器
	PlayerAttributeManager:Initialize()

	-- 监听玩家加入事件
	Players.PlayerAdded:Connect(function(player)
		self:SetupPlayerDowningHandler(player)
	end)

	-- 处理已在服务器的玩家
	for _, player in ipairs(Players:GetPlayers()) do
		self:SetupPlayerDowningHandler(player)
	end

	-- 玩家离开时清理数据
	Players.PlayerRemoving:Connect(function(player)
		self.DownedPlayers[player.UserId] = nil
		self.OriginalProperties[player.UserId] = nil
		-- 清理属性管理器中的数据
		PlayerAttributeManager:ClearPlayerStates(player)
	end)

	-- 注册客户端事件处理
	self:RegisterClientEvents()

	print("PlayerDownedService 初始化完成")
end

-- 注册客户端事件处理
function PlayerDownedService:RegisterClientEvents()
	-- 监听玩家自我复活事件
	NotifyService.RegisterServerEvent("PlayerSelfRevive", function(player, data)
		self:HandlePlayerSelfRevive(player)
	end)

	-- 监听检查游戏结束条件事件
	NotifyService.RegisterServerEvent("CheckGameEndCondition", function(player, data)
		print("收到客户端游戏结束检查请求，来自玩家: " .. player.Name)
		self:CheckGameEndAfterSelfReviveTimeout()
	end)
end

-- 为玩家设置濒死处理器
function PlayerDownedService:SetupPlayerDowningHandler(player)
	local function onCharacterAdded(character)
		-- 获取人形对象
		local humanoid = character:WaitForChild("Humanoid")

		-- 确保人物不会散架
		humanoid.BreakJointsOnDeath = false

		-- 保存原始属性
		self.OriginalProperties[player.UserId] = {
			walkSpeed = humanoid.WalkSpeed,
			jumpPower = humanoid.JumpPower,
			health = humanoid.Health,
			maxHealth = humanoid.MaxHealth
		}

		-- 监听健康值变化事件，用于检测濒死状态
		humanoid:GetPropertyChangedSignal("Health"):Connect(function()
			-- 当血量低于或等于0时，进入濒死状态
			if humanoid.Health <= 0 and not self.DownedPlayers[player.UserId] then
				-- 拦截死亡，设置为濒死状态
				self:HandlePlayerDowning(player, character)
			end
		end)

		-- 备份监听死亡事件，以防上面的方法失效
		humanoid.Died:Connect(function()
			-- 如果没有已经进入濒死状态，则进入
			if not self.DownedPlayers[player.UserId] then
				self:HandlePlayerDowning(player, character)
			end
		end)
	end

	-- 如果玩家已有角色，设置监听
	if player.Character then
		onCharacterAdded(player.Character)
	end

	-- 监听角色添加事件
	player.CharacterAdded:Connect(onCharacterAdded)
end

-- 处理玩家濒死
function PlayerDownedService:HandlePlayerDowning(player, character)
	print(player.Name .. " 血量到0，进入濒死状态")

	-- 标记玩家为濒死状态
	self.DownedPlayers[player.UserId] = true

	-- 保存当前属性并应用濒死状态属性
	local attributeSaved = PlayerAttributeManager:EnterDownedState(player)
	if attributeSaved then
		print("已保存", player.Name, "的当前属性并应用濒死状态")
	else
		warn("保存", player.Name, "的属性失败，使用备用方案")
	end

	-- 防止默认死亡处理
	local humanoid = character:FindFirstChildOfClass("Humanoid")
	if humanoid then
		-- 恢复少量血量以防止死亡
		humanoid.Health = 1

		-- 设置状态为濒死
		humanoid:SetStateEnabled(Enum.HumanoidStateType.Dead, false)

		-- 添加明确的濒死标记，便于AI和其他系统识别
		character:SetAttribute("IsDowned", true)
		player:SetAttribute("IsDowned", true)

		-- 强制卸下当前装备的武器
		local tool = character:FindFirstChildOfClass("Tool")
		if tool then
			tool.Parent = player.Backpack
		end
	end

	-- 在玩家进入濒死状态时立即创建死亡盒子
	local DeathBoxService = require(ReplicatedStorage.Scripts.Server.Services.DeathBoxService)
	DeathBoxService:HandlePlayerDowned(player, character)

	-- 通知客户端玩家已进入濒死状态
	NotifyService.FireClient("PlayerDowned", player, {})

	-- 广播给其他玩家
	NotifyService.FireAllExceptClient("PlayerDownedBroadcast", player, {
		playerName = player.Name,
		playerId = player.UserId,
		isDowned = true
	})

	-- 检查是否所有玩家都濒死
	self:CheckAllPlayersDownedStatus()

	-- 设置濒死状态的持续检查
	spawn(function()
		while self.DownedPlayers[player.UserId] do
			-- 如果角色不存在或已经被摧毁，退出循环
			if not player.Character or not player.Character:FindFirstChild("Humanoid") then
				break
			end

			-- 确保玩家仍处于濒死状态
			local currentHumanoid = player.Character:FindFirstChild("Humanoid")
			if currentHumanoid then
				if currentHumanoid.Health <= 0 then
					-- 防止死亡
					currentHumanoid.Health = 1
				end

				-- 防止自动回血
				if currentHumanoid.Health > 1 then
					currentHumanoid.Health = 1
				end
			end

			wait(0.1)
		end
	end)
end

-- 检查玩家是否处于濒死状态
function PlayerDownedService:IsPlayerDowned(player)
	if not player then return false end
	return self.DownedPlayers[player.UserId] == true
end

-- 恢复玩家（退出濒死状态）
function PlayerDownedService:RevivePlayer(player)
	if not player or not self.DownedPlayers[player.UserId] then return end

	print("🔄 开始复活玩家: " .. player.Name)

	-- 设置复活状态标记，防止死亡事件干扰
	player:SetAttribute("IsReviving", true)

	-- 移除濒死状态
	self.DownedPlayers[player.UserId] = nil

	-- 恢复角色正常状态
	if player.Character then
		local humanoid = player.Character:FindFirstChildOfClass("Humanoid")
		if humanoid then
			-- 恢复濒死前的属性（包含装备加成）
			local attributeRestored = PlayerAttributeManager:ExitDownedState(player)
			if attributeRestored then
				print("已恢复", player.Name, "的濒死前属性（包含装备加成）")
			else
				warn("恢复", player.Name, "的属性失败，使用备用方案")
				-- 备用方案：使用保存的原始属性
				local originalProps = self.OriginalProperties[player.UserId]
				if originalProps then
					humanoid.WalkSpeed = originalProps.walkSpeed
					humanoid.JumpPower = originalProps.jumpPower
				end
			end

			-- 移除濒死标记
			player.Character:SetAttribute("IsDowned", false)
			player:SetAttribute("IsDowned", false)

			-- 恢复血量到满血（在移除标记后，避免触发死亡事件）
			humanoid.Health = humanoid.MaxHealth

			-- 延迟移除复活标记，确保复活过程完全完成
			spawn(function()
				wait(2) -- 等待2秒确保复活过程稳定
				player:SetAttribute("IsReviving", false)
				print("✅ 玩家 " .. player.Name .. " 复活过程完成")
			end)
		end
	end

	-- 通知客户端玩家已恢复，并同步最新属性
	local currentAttrs = PlayerAttributeManager:GetCurrentAttributes(player)
	NotifyService.FireClient("PlayerRevived", player, {
		walkSpeed = currentAttrs and currentAttrs.walkSpeed or 16,
		jumpPower = currentAttrs and currentAttrs.jumpPower or 50
	})

	-- 广播给其他玩家
	NotifyService.FireAllClient("PlayerRevivedBroadcast", {
		playerName = player.Name,
		playerId = player.UserId,
		isDowned = false
	})

	-- 检查是否所有玩家都濒死（复活后可能改变状态）
	self:CheckAllPlayersDownedStatus()

	-- 清理可能残留的ItemUI数据
	local DeathBoxService = require(ReplicatedStorage.Scripts.Server.Services.DeathBoxService)
	DeathBoxService:CleanupPlayerItemUIData(player.UserId)
end

-- 使玩家完全死亡（跳过濒死状态，直接死亡）
function PlayerDownedService:KillPlayer(player)
	if not player then return end

	-- 如果玩家处于濒死状态，先清除
	if self.DownedPlayers[player.UserId] then
		self.DownedPlayers[player.UserId] = nil
	end

	-- 使角色死亡
	if player.Character and player.Character:FindFirstChildOfClass("Humanoid") then
		-- 移除濒死标记
		player.Character:SetAttribute("IsDowned", false)
		player:SetAttribute("IsDowned", false)

		-- 设置血量为0，触发真正的死亡
		player.Character.Humanoid.Health = 0
	end
end

-- 检查所有玩家濒死状态
function PlayerDownedService:CheckAllPlayersDownedStatus()
	local allPlayersDowned = true
	local totalPlayers = 0
	local downedPlayers = 0

	-- 检查所有玩家状态
	for _, player in ipairs(Players:GetPlayers()) do
		totalPlayers = totalPlayers + 1
		local isDowned = self.DownedPlayers[player.UserId] == true
		if isDowned then
			downedPlayers = downedPlayers + 1
			print("玩家 " .. player.Name .. " 处于濒死状态")
		else
			allPlayersDowned = false
			print("玩家 " .. player.Name .. " 仍然存活")
		end
	end

	print("服务端玩家状态检查: " .. downedPlayers .. "/" .. totalPlayers .. " 玩家濒死")

	if allPlayersDowned and totalPlayers > 0 then
		-- 修复：无论单人还是多人游戏，都等待自我复活时间结束
		print("所有玩家都濒死，但等待自我复活时间结束后再判断游戏结束")
		print("玩家数量:", totalPlayers, "濒死玩家数量:", downedPlayers)
		-- 不立即触发游戏结束，让所有客户端的自我复活倒计时正常进行
		-- 游戏结束将在客户端自我复活超时后通过NotifyServerCheckGameEnd触发
		return
	else
		print("游戏继续进行，还有存活的玩家")
	end
end

-- 检查自我复活超时后的游戏结束条件（专门处理单人游戏场景）
function PlayerDownedService:CheckGameEndAfterSelfReviveTimeout()
	local allPlayersDowned = true
	local totalPlayers = 0
	local downedPlayers = 0

	-- 检查所有玩家状态
	for _, player in ipairs(Players:GetPlayers()) do
		totalPlayers = totalPlayers + 1
		local isDowned = self.DownedPlayers[player.UserId] == true
		if isDowned then
			downedPlayers = downedPlayers + 1
			print("玩家 " .. player.Name .. " 处于濒死状态")
		else
			allPlayersDowned = false
			print("玩家 " .. player.Name .. " 仍然存活")
		end
	end

	print("自我复活超时后的玩家状态检查: " .. downedPlayers .. "/" .. totalPlayers .. " 玩家濒死")

	if allPlayersDowned and totalPlayers > 0 then
		-- 所有玩家都濒死且自我复活时间已结束，触发游戏结束
		print("所有玩家都濒死且自我复活时间已结束，触发游戏结束")

		-- 通知所有客户端游戏结束
		NotifyService.FireAllClient("GameOver", {
			totalPlayers = totalPlayers,
			downedPlayers = downedPlayers,
			immediate = true, -- 标记为立即游戏结束
			reason = "selfReviveTimeout", -- 标记游戏结束原因
			enableTeleportCountdown = true -- 新增：启用传送倒计时
		})
		print("GameOver事件已发送给所有客户端（自我复活超时）")
	else
		print("游戏继续进行，还有存活的玩家或玩家已复活")
	end
end

-- 处理玩家自我复活
function PlayerDownedService:HandlePlayerSelfRevive(player)
	print("处理玩家自我复活: " .. player.Name)

	-- 检查玩家是否确实处于濒死状态
	if not self.DownedPlayers[player.UserId] then
		warn("玩家 " .. player.Name .. " 不在濒死状态，无法自我复活")
		return
	end

	-- 执行复活
	self:RevivePlayer(player)

	print("玩家 " .. player.Name .. " 自我复活成功")
end

return PlayerDownedService