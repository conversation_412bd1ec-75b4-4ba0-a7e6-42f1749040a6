--!strict
-- 传送数据测试脚本
-- 用于验证新的动态玩家物品数据系统

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TeleportDataManager = require(ReplicatedStorage.Scripts.Server.Services.TeleportDataManager)

local TeleportDataTest = {}

-- 模拟新格式传送数据
local function createNewFormatTestData()
	return {
		sourcePlace = 12345,
		timestamp = os.time(),
		playerItemData = {
			["123456"] = {
				playerName = "TestPlayer1",
				occupationId = 4001,
				items = {
					{id = 10031, quantity = 1, itemType = 9}, -- 木棒
					{id = 10052, quantity = 1, itemType = 10}, -- 冲锋枪
					{id = 10026, quantity = 5, itemType = 7}, -- 绷带
				}
			},
			["789012"] = {
				playerName = "TestPlayer2", 
				occupationId = 4002,
				items = {
					{id = 10039, quantity = 1, itemType = 11}, -- 铁头盔
					{id = 10029, quantity = 30, itemType = 6}, -- 手枪子弹
				}
			}
		},
		professionPlayer = {
			["123456"] = "战士",
			["789012"] = "法师"
		},
		professionState = {
			["123456"] = 4001,
			["789012"] = 4002
		}
	}
end

-- 模拟旧格式传送数据
local function createOldFormatTestData()
	return {
		job = "战士",
		str = "近战职业",
		items = {
			{id = 10031, quantity = 1, itemType = 9}, -- 木棒
			{id = 10026, quantity = 3, itemType = 7}, -- 绷带
		}
	}
end

-- 测试新格式数据处理
function TeleportDataTest.testNewFormat()
	print("=== 测试新格式数据处理 ===")
	
	local testData = createNewFormatTestData()
	local testPlayer = Players:GetPlayers()[1]
	
	if not testPlayer then
		warn("没有玩家可用于测试")
		return false
	end
	
	-- 模拟设置玩家ID为测试数据中的ID
	local originalUserId = testPlayer.UserId
	testPlayer.UserId = 123456 -- 临时修改用于测试
	
	-- 设置测试数据
	local success = TeleportDataManager:SetTestTeleportData(testPlayer, testData)
	if not success then
		warn("设置测试数据失败")
		testPlayer.UserId = originalUserId
		return false
	end
	
	-- 测试获取物品数据
	local items = TeleportDataManager:GetPlayerItems(testPlayer)
	if not items then
		warn("获取物品数据失败")
		testPlayer.UserId = originalUserId
		return false
	end
	
	print("✅ 成功获取物品数据，数量:", #items)
	for i, item in ipairs(items) do
		print(string.format("  物品%d: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
	end
	
	-- 测试获取职业信息
	local job, jobDesc = TeleportDataManager:GetPlayerJob(testPlayer)
	local occupationId = TeleportDataManager:GetPlayerOccupationId(testPlayer)
	
	print("职业信息:", job or "无", jobDesc or "无")
	print("职业ID:", occupationId or "无")
	
	-- 恢复原始用户ID
	testPlayer.UserId = originalUserId
	
	print("=== 新格式测试完成 ===\n")
	return true
end

-- 测试旧格式数据处理
function TeleportDataTest.testOldFormat()
	print("=== 测试旧格式数据处理 ===")
	
	local testData = createOldFormatTestData()
	local testPlayer = Players:GetPlayers()[1]
	
	if not testPlayer then
		warn("没有玩家可用于测试")
		return false
	end
	
	-- 设置测试数据
	local success = TeleportDataManager:SetTestTeleportData(testPlayer, testData)
	if not success then
		warn("设置测试数据失败")
		return false
	end
	
	-- 测试获取物品数据
	local items = TeleportDataManager:GetPlayerItems(testPlayer)
	if not items then
		warn("获取物品数据失败")
		return false
	end
	
	print("✅ 成功获取物品数据，数量:", #items)
	for i, item in ipairs(items) do
		print(string.format("  物品%d: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
	end
	
	-- 测试获取职业信息
	local job, jobDesc = TeleportDataManager:GetPlayerJob(testPlayer)
	print("职业信息:", job or "无", jobDesc or "无")
	
	print("=== 旧格式测试完成 ===\n")
	return true
end

-- 运行所有测试
function TeleportDataTest.runAllTests()
	print("🚀 开始传送数据测试")
	
	local newFormatResult = TeleportDataTest.testNewFormat()
	local oldFormatResult = TeleportDataTest.testOldFormat()
	
	if newFormatResult and oldFormatResult then
		print("✅ 所有测试通过！")
		return true
	else
		warn("❌ 部分测试失败")
		return false
	end
end

-- 测试数据验证功能
function TeleportDataTest.testDataValidation()
	print("=== 测试数据验证功能 ===")
	
	-- 测试有效的新格式数据
	local validNewData = createNewFormatTestData()
	print("测试有效新格式数据...")
	
	-- 测试有效的旧格式数据
	local validOldData = createOldFormatTestData()
	print("测试有效旧格式数据...")
	
	-- 测试无效数据
	local invalidData = {
		playerItemData = {
			["123"] = {
				-- 缺少必要字段
				items = {}
			}
		}
	}
	print("测试无效数据...")
	
	print("=== 数据验证测试完成 ===\n")
end

return TeleportDataTest
