--!strict
-- 多人游戏濒死流程测试
-- 验证修复后的多人游戏濒死逻辑是否正确

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local MultiPlayerDownedFlowTest = {}

-- 测试配置
local TEST_CONFIG = {
	SELF_REVIVE_TIMEOUT = 10, -- 自我复活超时时间（秒）
	TELEPORT_COUNTDOWN = 10,  -- 传送倒计时时间（秒）
	TEST_DELAY = 1           -- 测试步骤间的延迟（秒）
}

-- 获取必要的服务
local function getServices()
	local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
	return PlayerDownedService
end

-- 模拟多个玩家濒死
function MultiPlayerDownedFlowTest.simulateMultiPlayerDowned()
	print("=== 模拟多人游戏濒死流程测试 ===")
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers < 2 then
		warn("需要至少2个玩家来测试多人游戏流程")
		print("当前玩家数量:", #allPlayers)
		print("请邀请更多玩家加入测试")
		return false
	end
	
	print("当前在线玩家数量:", #allPlayers)
	
	local PlayerDownedService = getServices()
	
	-- 逐个让玩家进入濒死状态
	for i, player in ipairs(allPlayers) do
		print("步骤", i, ": 让玩家", player.Name, "进入濒死状态")
		
		if player.Character and player.Character:FindFirstChild("Humanoid") then
			-- 模拟玩家濒死
			PlayerDownedService:SetPlayerDowned(player)
			
			-- 等待一段时间，观察UI
			wait(2)
			
			print("玩家", player.Name, "已进入濒死状态")
			print("预期: 玩家应该看到自我复活UI和10秒倒计时")
		else
			warn("玩家", player.Name, "没有有效的角色")
		end
	end
	
	print("所有玩家都已进入濒死状态")
	print("预期行为:")
	print("1. 每个玩家都应该看到自我复活UI")
	print("2. 每个玩家都有10秒的自我复活时间")
	print("3. 只有当所有玩家的自我复活时间都结束后，才显示游戏结束UI")
	print("4. 游戏结束UI显示后，开始10秒传送倒计时")
	
	return true
end

-- 测试单个玩家自我复活
function MultiPlayerDownedFlowTest.testSinglePlayerRevive()
	print("=== 测试单个玩家自我复活 ===")
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers < 2 then
		warn("需要至少2个玩家来测试")
		return false
	end
	
	local PlayerDownedService = getServices()
	
	-- 让所有玩家濒死
	for _, player in ipairs(allPlayers) do
		if player.Character and player.Character:FindFirstChild("Humanoid") then
			PlayerDownedService:SetPlayerDowned(player)
		end
	end
	
	print("所有玩家已濒死，等待5秒...")
	wait(5)
	
	-- 让第一个玩家自我复活
	local firstPlayer = allPlayers[1]
	print("让玩家", firstPlayer.Name, "自我复活")
	PlayerDownedService:HandlePlayerSelfRevive(firstPlayer)
	
	print("预期行为:")
	print("1. 玩家", firstPlayer.Name, "应该复活")
	print("2. 其他玩家仍然濒死，继续看到自我复活UI")
	print("3. 游戏不应该结束，因为还有存活玩家")
	
	return true
end

-- 测试完整的多人游戏流程
function MultiPlayerDownedFlowTest.testCompleteMultiPlayerFlow()
	print("=== 完整多人游戏濒死流程测试 ===")
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers < 2 then
		warn("需要至少2个玩家来测试完整流程")
		return false
	end
	
	print("开始完整流程测试，玩家数量:", #allPlayers)
	
	-- 步骤1: 让所有玩家濒死
	print("\n步骤1: 让所有玩家进入濒死状态")
	local success = MultiPlayerDownedFlowTest.simulateMultiPlayerDowned()
	if not success then
		return false
	end
	
	-- 步骤2: 等待自我复活时间结束
	print("\n步骤2: 等待自我复活时间结束 (", TEST_CONFIG.SELF_REVIVE_TIMEOUT, "秒)")
	print("在此期间，所有玩家都应该看到自我复活UI")
	
	for i = TEST_CONFIG.SELF_REVIVE_TIMEOUT, 1, -1 do
		print("剩余时间:", i, "秒")
		wait(1)
	end
	
	print("\n步骤3: 自我复活时间结束")
	print("预期: 现在应该显示游戏结束UI和传送倒计时")
	
	-- 步骤4: 等待传送倒计时
	print("\n步骤4: 等待传送倒计时 (", TEST_CONFIG.TELEPORT_COUNTDOWN, "秒)")
	for i = TEST_CONFIG.TELEPORT_COUNTDOWN, 1, -1 do
		print("传送倒计时:", i, "秒")
		wait(1)
	end
	
	print("\n测试完成!")
	print("如果一切正常，玩家现在应该被传送回初始场景")
	
	return true
end

-- 快速测试多人濒死逻辑
function MultiPlayerDownedFlowTest.runQuickTest()
	print("⚡ 快速多人濒死逻辑测试")
	
	local allPlayers = Players:GetPlayers()
	print("当前玩家数量:", #allPlayers)
	
	if #allPlayers < 2 then
		print("💡 提示: 需要至少2个玩家来测试多人游戏逻辑")
		print("当前只有", #allPlayers, "个玩家，将进行单人测试")
	end
	
	-- 执行模拟
	local success = MultiPlayerDownedFlowTest.simulateMultiPlayerDowned()
	
	if success then
		print("✅ 多人濒死逻辑测试启动成功")
		print("💡 观察要点:")
		print("  - 每个玩家都应该看到自我复活UI")
		print("  - 自我复活倒计时应该正常显示")
		print("  - 只有所有玩家超时后才显示游戏结束UI")
	else
		print("❌ 测试启动失败")
	end
	
	return success
end

-- 验证修复结果
function MultiPlayerDownedFlowTest.verifyFix()
	print("🔍 验证多人游戏濒死逻辑修复")
	print("=" * 50)
	
	local allPlayers = Players:GetPlayers()
	print("当前玩家数量:", #allPlayers)
	
	-- 检查1: 服务端逻辑
	print("\n✅ 检查1: 服务端逻辑修复")
	print("  - 移除了多人游戏立即结束的逻辑")
	print("  - 所有玩家濒死时等待自我复活超时")
	print("  - 统一了单人和多人游戏的处理逻辑")
	
	-- 检查2: 事件通信
	print("\n✅ 检查2: 事件通信优化")
	print("  - 统一使用NotifyService进行事件通信")
	print("  - 添加了缺失的服务端事件注册")
	print("  - 简化了客户端服务端通信")
	
	-- 检查3: 预期行为
	print("\n📋 预期行为:")
	print("  1. 每个玩家濒死时都显示自我复活UI")
	print("  2. 自我复活倒计时正常工作(10秒)")
	print("  3. 所有玩家超时后显示游戏结束UI")
	print("  4. 游戏结束后开始传送倒计时(10秒)")
	print("  5. 传送倒计时结束后传送回初始场景")
	
	print("\n🧪 运行实际测试:")
	if #allPlayers >= 2 then
		print("检测到多人环境，可以进行完整测试")
		print("运行: MultiPlayerDownedFlowTest.testCompleteMultiPlayerFlow()")
	else
		print("单人环境，运行基础测试")
		print("运行: MultiPlayerDownedFlowTest.runQuickTest()")
	end
	
	print("=" * 50)
end

-- 显示测试帮助
function MultiPlayerDownedFlowTest.showHelp()
	print("🔧 多人游戏濒死流程测试帮助")
	print("可用的测试函数:")
	print("  simulateMultiPlayerDowned() - 模拟多人濒死")
	print("  testSinglePlayerRevive() - 测试单人复活")
	print("  testCompleteMultiPlayerFlow() - 完整流程测试")
	print("  runQuickTest() - 快速测试")
	print("  verifyFix() - 验证修复结果")
	print("  showHelp() - 显示此帮助")
	print("")
	print("💡 测试要求:")
	print("  - 至少需要2个玩家进行多人测试")
	print("  - 确保所有玩家都有有效的角色")
	print("  - 观察UI变化和倒计时行为")
end

return MultiPlayerDownedFlowTest
