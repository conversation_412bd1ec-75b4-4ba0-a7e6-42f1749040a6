--!strict
-- 游戏结束传送流程测试脚本
-- 测试修改后的流程：濒死→自我复活→游戏结束→传送倒计时

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local GameOverTeleportFlowTest = {}

-- 测试配置
local TEST_DELAY = 2 -- 测试步骤之间的延迟

-- 模拟玩家濒死
function GameOverTeleportFlowTest.simulatePlayerDowned(player)
	print("=== 模拟玩家濒死：", player.Name, " ===")
	
	if not player.Character or not player.Character:FindFirstChild("Humanoid") then
		warn("玩家", player.Name, "没有有效的角色")
		return false
	end
	
	-- 直接调用濒死处理函数
	PlayerDownedService:HandlePlayerDowning(player, player.Character)
	print("✅ 玩家", player.Name, "已进入濒死状态")
	return true
end

-- 模拟所有玩家濒死
function GameOverTeleportFlowTest.simulateAllPlayersDowned()
	print("=== 模拟所有玩家濒死 ===")
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers == 0 then
		warn("没有玩家可用于测试")
		return false
	end
	
	print("当前在线玩家数量:", #allPlayers)
	
	local successCount = 0
	for _, player in ipairs(allPlayers) do
		if GameOverTeleportFlowTest.simulatePlayerDowned(player) then
			successCount = successCount + 1
		end
	end
	
	print("成功模拟", successCount, "/", #allPlayers, "个玩家濒死")
	return successCount == #allPlayers
end

-- 测试单人游戏流程
function GameOverTeleportFlowTest.testSinglePlayerFlow()
	print("🧪 测试单人游戏流程")
	print("=" * 40)
	
	local testPlayer = Players:GetPlayers()[1]
	if not testPlayer then
		warn("没有玩家可用于测试")
		return false
	end
	
	print("测试玩家:", testPlayer.Name)
	
	-- 步骤1: 模拟玩家濒死
	print("\n📋 步骤1: 模拟玩家濒死")
	local success = GameOverTeleportFlowTest.simulatePlayerDowned(testPlayer)
	if not success then
		warn("❌ 步骤1失败")
		return false
	end
	
	print("✅ 步骤1完成 - 玩家应该看到自我复活UI")
	print("💡 请观察客户端是否显示自我复活倒计时")
	
	-- 等待自我复活倒计时结束（约10秒）
	print("\n⏳ 等待自我复活倒计时结束...")
	wait(12) -- 等待12秒确保倒计时结束
	
	print("✅ 单人游戏流程测试完成")
	print("💡 玩家应该看到游戏结束UI并开始传送倒计时")
	
	return true
end

-- 测试多人游戏流程
function GameOverTeleportFlowTest.testMultiPlayerFlow()
	print("🧪 测试多人游戏流程")
	print("=" * 40)
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers < 2 then
		warn("需要至少2个玩家来测试多人游戏流程")
		return false
	end
	
	print("测试玩家数量:", #allPlayers)
	
	-- 步骤1: 模拟所有玩家濒死
	print("\n📋 步骤1: 模拟所有玩家濒死")
	local success = GameOverTeleportFlowTest.simulateAllPlayersDowned()
	if not success then
		warn("❌ 步骤1失败")
		return false
	end
	
	print("✅ 步骤1完成 - 所有玩家应该立即看到游戏结束UI")
	print("💡 请观察客户端是否跳过自我复活直接显示游戏结束UI")
	print("💡 游戏结束UI应该显示传送倒计时")
	
	return true
end

-- 测试传送倒计时取消（玩家复活）
function GameOverTeleportFlowTest.testCountdownCancellation()
	print("🧪 测试传送倒计时取消")
	print("=" * 40)
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers == 0 then
		warn("没有玩家可用于测试")
		return false
	end
	
	-- 步骤1: 模拟所有玩家濒死
	print("\n📋 步骤1: 模拟所有玩家濒死")
	local success = GameOverTeleportFlowTest.simulateAllPlayersDowned()
	if not success then
		warn("❌ 步骤1失败")
		return false
	end
	
	-- 等待几秒让倒计时开始
	print("\n⏳ 等待倒计时开始...")
	wait(3)
	
	-- 步骤2: 复活一个玩家
	print("\n📋 步骤2: 复活一个玩家")
	local testPlayer = allPlayers[1]
	PlayerDownedService:RevivePlayer(testPlayer)
	print("✅ 玩家", testPlayer.Name, "已复活")
	
	print("💡 游戏应该继续，传送倒计时应该被取消")
	
	return true
end

-- 完整流程测试
function GameOverTeleportFlowTest.runCompleteFlowTest()
	print("🚀 开始完整流程测试")
	print("=" * 50)
	
	local allPlayers = Players:GetPlayers()
	local playerCount = #allPlayers
	
	print("当前玩家数量:", playerCount)
	
	if playerCount == 0 then
		warn("❌ 没有玩家，无法进行测试")
		return false
	elseif playerCount == 1 then
		print("🔍 检测到单人游戏，运行单人流程测试")
		return GameOverTeleportFlowTest.testSinglePlayerFlow()
	else
		print("🔍 检测到多人游戏，运行多人流程测试")
		return GameOverTeleportFlowTest.testMultiPlayerFlow()
	end
end

-- 快速测试（仅测试核心功能）
function GameOverTeleportFlowTest.runQuickTest()
	print("⚡ 快速测试：游戏结束传送流程")
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers == 0 then
		warn("❌ 没有玩家可用于测试")
		return false
	end
	
	-- 模拟所有玩家濒死
	local success = GameOverTeleportFlowTest.simulateAllPlayersDowned()
	if success then
		print("✅ 快速测试启动成功")
		print("💡 请观察游戏结束UI和传送倒计时")
		print("💡 倒计时结束后应该自动传送回初始场景")
	else
		warn("❌ 快速测试启动失败")
	end
	
	return success
end

-- 手动触发游戏结束（用于测试UI）
function GameOverTeleportFlowTest.triggerGameOverUI()
	print("🎮 手动触发游戏结束UI")
	
	-- 直接发送GameOver事件给所有客户端
	NotifyService.FireAllClient("GameOver", {
		totalPlayers = #Players:GetPlayers(),
		downedPlayers = #Players:GetPlayers(),
		immediate = true,
		enableTeleportCountdown = true,
		reason = "manualTest"
	})
	
	print("✅ GameOver事件已发送")
	print("💡 所有客户端应该显示游戏结束UI和传送倒计时")
end

-- 紧急停止测试
function GameOverTeleportFlowTest.emergencyStop()
	print("🛑 紧急停止所有测试")
	
	-- 复活所有玩家
	for _, player in ipairs(Players:GetPlayers()) do
		if PlayerDownedService:IsPlayerDowned(player) then
			PlayerDownedService:RevivePlayer(player)
			print("复活玩家:", player.Name)
		end
	end
	
	print("✅ 紧急停止完成")
end

-- 显示测试帮助信息
function GameOverTeleportFlowTest.showHelp()
	print("🔧 游戏结束传送流程测试帮助")
	print("可用的测试函数:")
	print("  runCompleteFlowTest() - 运行完整流程测试")
	print("  runQuickTest() - 快速测试核心功能")
	print("  testSinglePlayerFlow() - 测试单人游戏流程")
	print("  testMultiPlayerFlow() - 测试多人游戏流程")
	print("  testCountdownCancellation() - 测试倒计时取消")
	print("  triggerGameOverUI() - 手动触发游戏结束UI")
	print("  emergencyStop() - 紧急停止所有测试")
	print("  showHelp() - 显示此帮助信息")
	print("")
	print("🎯 预期流程:")
	print("  单人: 濒死 → 自我复活UI → 倒计时结束 → 游戏结束UI → 传送倒计时")
	print("  多人: 濒死 → 立即游戏结束UI → 传送倒计时")
end

-- 验证流程状态
function GameOverTeleportFlowTest.verifyFlowState()
	print("🔍 验证当前流程状态")
	
	local allPlayers = Players:GetPlayers()
	print("在线玩家数量:", #allPlayers)
	
	local downedCount = 0
	for _, player in ipairs(allPlayers) do
		local isDowned = PlayerDownedService:IsPlayerDowned(player)
		print("  玩家", player.Name, "濒死状态:", isDowned)
		if isDowned then
			downedCount = downedCount + 1
		end
	end
	
	print("濒死玩家数量:", downedCount, "/", #allPlayers)
	
	if downedCount == #allPlayers and #allPlayers > 0 then
		print("🚨 所有玩家都濒死，应该触发游戏结束")
	elseif downedCount > 0 then
		print("⚠️ 部分玩家濒死，游戏继续")
	else
		print("✅ 所有玩家正常，游戏进行中")
	end
end

return GameOverTeleportFlowTest
