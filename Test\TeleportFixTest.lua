--!strict
-- 传送修复测试脚本
-- 用于验证传送失败问题的修复

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TeleportExecutorService = require(ReplicatedStorage.Scripts.Server.Services.TeleportExecutorService)
local TeleportConfig = require(ReplicatedStorage.Scripts.Config.TeleportConfig)
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local TeleportFixTest = {}

-- 测试传送执行服务
function TeleportFixTest.testTeleportExecutorService()
	print("=== 测试传送执行服务 ===")
	
	local testPlayer = Players:GetPlayers()[1]
	if not testPlayer then
		warn("没有玩家可用于测试")
		return false
	end
	
	local targetPlaceId = TeleportConfig:GetInitialPlaceId()
	print("测试玩家:", testPlayer.Name)
	print("目标场景ID:", targetPlaceId)
	
	-- 测试传送统计信息
	local stats = TeleportExecutorService:GetTeleportStats()
	print("传送统计信息:")
	print("  当前场景ID:", stats.currentPlaceId)
	print("  目标场景ID:", stats.targetPlaceId)
	print("  玩家数量:", stats.playerCount)
	
	return true
end

-- 测试客户端传送请求
function TeleportFixTest.testClientTeleportRequest()
	print("=== 测试客户端传送请求 ===")
	
	local testPlayer = Players:GetPlayers()[1]
	if not testPlayer then
		warn("没有玩家可用于测试")
		return false
	end
	
	-- 模拟客户端发送传送请求
	print("模拟客户端传送请求...")
	
	-- 直接调用服务端处理函数（模拟RemoteEvent）
	TeleportExecutorService:HandleTeleportRequest(testPlayer, {
		targetPlaceId = TeleportConfig:GetInitialPlaceId(),
		reason = "test",
		timestamp = os.time()
	})
	
	print("✅ 传送请求已处理")
	return true
end

-- 测试传送失败处理
function TeleportFixTest.testTeleportFailureHandling()
	print("=== 测试传送失败处理 ===")
	
	local testPlayer = Players:GetPlayers()[1]
	if not testPlayer then
		warn("没有玩家可用于测试")
		return false
	end
	
	-- 测试无效场景ID
	print("测试无效场景ID...")
	TeleportExecutorService:HandleTeleportRequest(testPlayer, {
		targetPlaceId = -1, -- 无效的场景ID
		reason = "test",
		timestamp = os.time()
	})
	
	-- 测试无效请求数据
	print("测试无效请求数据...")
	TeleportExecutorService:HandleTeleportRequest(testPlayer, nil)
	
	print("✅ 传送失败处理测试完成")
	return true
end

-- 测试配置文件
function TeleportFixTest.testTeleportConfig()
	print("=== 测试传送配置 ===")
	
	-- 显示当前配置
	TeleportConfig:ShowConfig()
	
	-- 验证配置
	local isValid, errors = TeleportConfig:ValidateConfig()
	if isValid then
		print("✅ 配置验证通过")
	else
		warn("❌ 配置验证失败:")
		for _, error in ipairs(errors) do
			warn("  - " .. error)
		end
	end
	
	return isValid
end

-- 测试NotifyService事件
function TeleportFixTest.testNotifyServiceEvents()
	print("=== 测试NotifyService事件 ===")
	
	local testPlayer = Players:GetPlayers()[1]
	if not testPlayer then
		warn("没有玩家可用于测试")
		return false
	end
	
	-- 测试传送开始事件
	print("测试传送开始事件...")
	NotifyService.FireClient(testPlayer, "TeleportToInitialStart", {
		targetPlaceId = TeleportConfig:GetInitialPlaceId(),
		reason = "test"
	})
	
	-- 测试传送失败事件
	print("测试传送失败事件...")
	NotifyService.FireClient(testPlayer, "TeleportToInitialFailed", {
		error = "测试错误信息",
		timestamp = os.time()
	})
	
	print("✅ NotifyService事件测试完成")
	return true
end

-- 完整的修复验证测试
function TeleportFixTest.runCompleteFixTest()
	print("🚀 开始完整的传送修复验证测试")
	print("=" * 50)
	
	local results = {}
	
	-- 测试1: 传送执行服务
	print("\n📋 测试1: 传送执行服务")
	results.executorService = TeleportFixTest.testTeleportExecutorService()
	
	-- 测试2: 传送配置
	print("\n📋 测试2: 传送配置")
	results.config = TeleportFixTest.testTeleportConfig()
	
	-- 测试3: 客户端传送请求
	print("\n📋 测试3: 客户端传送请求")
	results.clientRequest = TeleportFixTest.testClientTeleportRequest()
	
	-- 测试4: 传送失败处理
	print("\n📋 测试4: 传送失败处理")
	results.failureHandling = TeleportFixTest.testTeleportFailureHandling()
	
	-- 测试5: NotifyService事件
	print("\n📋 测试5: NotifyService事件")
	results.notifyEvents = TeleportFixTest.testNotifyServiceEvents()
	
	-- 输出测试结果
	print("\n" .. "=" * 50)
	print("🏁 传送修复验证结果:")
	print("  传送执行服务:", results.executorService and "✅ 通过" or "❌ 失败")
	print("  传送配置:", results.config and "✅ 通过" or "❌ 失败")
	print("  客户端传送请求:", results.clientRequest and "✅ 通过" or "❌ 失败")
	print("  传送失败处理:", results.failureHandling and "✅ 通过" or "❌ 失败")
	print("  NotifyService事件:", results.notifyEvents and "✅ 通过" or "❌ 失败")
	
	local allPassed = results.executorService and results.config and 
					  results.clientRequest and results.failureHandling and 
					  results.notifyEvents
	
	if allPassed then
		print("\n🎉 所有测试通过！传送修复验证成功")
		print("💡 现在传送应该在服务端正确执行，不会再出现客户端错误")
	else
		print("\n⚠️ 部分测试失败，请检查相关功能")
	end
	
	print("=" * 50)
	return allPassed
end

-- 快速测试传送功能
function TeleportFixTest.runQuickTeleportTest()
	print("⚡ 快速传送功能测试")
	
	local testPlayer = Players:GetPlayers()[1]
	if not testPlayer then
		warn("❌ 没有玩家可用于测试")
		return false
	end
	
	print("测试玩家:", testPlayer.Name)
	print("目标场景:", TeleportConfig:GetInitialPlaceId())
	
	-- 直接调用传送执行
	TeleportExecutorService:ManualTeleportPlayer(
		testPlayer, 
		TeleportConfig:GetInitialPlaceId(), 
		"quickTest"
	)
	
	print("✅ 快速传送测试已启动")
	print("💡 如果没有错误信息，说明修复成功")
	
	return true
end

-- 显示修复说明
function TeleportFixTest.showFixExplanation()
	print("🔧 传送失败修复说明")
	print("=" * 40)
	print("问题: TeleportUnknown must be called from a Server")
	print("原因: 在客户端调用了TeleportService:TeleportAsync()")
	print("")
	print("修复方案:")
	print("1. 客户端通过NotifyService发送传送请求")
	print("2. 服务端TeleportExecutorService接收请求")
	print("3. 服务端执行TeleportService:TeleportAsync()")
	print("4. 服务端通知客户端传送结果")
	print("")
	print("修改的文件:")
	print("- Scripts/Client/Services/PlayerDownedClient")
	print("- Scripts/Server/Services/TeleportExecutorService (新增)")
	print("- Scripts/Share/Services/NotifyService")
	print("- Scripts/Server/ServerEnter")
	print("")
	print("现在传送将在服务端正确执行！")
end

-- 显示测试帮助
function TeleportFixTest.showHelp()
	print("🔧 传送修复测试帮助")
	print("可用的测试函数:")
	print("  runCompleteFixTest() - 运行完整修复验证")
	print("  runQuickTeleportTest() - 快速传送功能测试")
	print("  testTeleportExecutorService() - 测试传送执行服务")
	print("  testTeleportConfig() - 测试传送配置")
	print("  testClientTeleportRequest() - 测试客户端请求")
	print("  testTeleportFailureHandling() - 测试失败处理")
	print("  showFixExplanation() - 显示修复说明")
	print("  showHelp() - 显示此帮助信息")
end

return TeleportFixTest
