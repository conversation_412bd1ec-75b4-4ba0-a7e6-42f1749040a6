# 濒死流程修复总结

## 🎯 修复目标

您希望保留原来的流程：
1. 玩家濒死 → 显示濒死UI + 自我复活倒计时（10秒）
2. 自我复活倒计时结束 → **显示游戏结束UI**
3. 在游戏结束UI上 → **显示10秒传送倒计时**

## 🔍 发现的问题

### 原始流程问题：
1. **自我复活超时后直接通知服务端** - 跳过了游戏结束UI显示
2. **服务端直接启动传送倒计时** - 没有先显示游戏结束UI
3. **UI切换不流畅** - 可能出现UI重复显示或缺失

## 🛠️ 修复内容

### 1. 修改 `OnSelfReviveTimeoutComplete()` 函数
**文件**: `Scripts\Client\Services\PlayerDownedClient`
**位置**: 第1337-1365行

**修改前**:
```lua
-- 延迟通知服务端检查游戏结束条件
spawn(function()
    wait(0.5)
    print("延迟后通知服务端检查游戏结束条件")
    self:NotifyServerCheckGameEnd()
end)
```

**修改后**:
```lua
-- 修改：先显示游戏结束UI，然后再通知服务端
print("显示游戏结束UI")
self:ShowGameOverUI()

-- 延迟通知服务端检查游戏结束条件
spawn(function()
    wait(0.5)
    print("延迟后通知服务端检查游戏结束条件")
    self:NotifyServerCheckGameEnd()
end)
```

### 2. 修改 `OnAllPlayersDowned()` 函数
**文件**: `Scripts\Client\Services\PlayerDownedClient`
**位置**: 第1460-1495行

**修改前**:
```lua
-- 显示传送倒计时提示（GameEndTeleportClient会处理具体的UI）
print("传送倒计时系统将接管UI显示")
```

**修改后**:
```lua
-- 修改：确保游戏结束UI已显示，然后在其上添加传送倒计时
if not self:IsGameOverUIVisible() then
    print("游戏结束UI未显示，先显示游戏结束UI")
    self:ShowGameOverUI()
end

-- 在游戏结束UI上添加传送倒计时
print("在游戏结束UI上添加传送倒计时")
self:AddTeleportCountdownToGameOverUI(data)
```

### 3. 新增辅助函数

#### `IsGameOverUIVisible()` - 检查游戏结束UI是否可见
**位置**: 第1628-1638行
```lua
function PlayerDownedClient:IsGameOverUIVisible()
    local player = Players.LocalPlayer
    if not player then return false end
    
    local playerGui = player:FindFirstChild("PlayerGui")
    if not playerGui then return false end
    
    local gameOverUI = playerGui:FindFirstChild("GameOverUI")
    return gameOverUI ~= nil and gameOverUI.Enabled
end
```

#### `RemoveGameOverUI()` - 移除游戏结束UI
**位置**: 第1640-1653行
```lua
function PlayerDownedClient:RemoveGameOverUI()
    local player = Players.LocalPlayer
    if not player then return end
    
    local playerGui = player:FindFirstChild("PlayerGui")
    if not playerGui then return end
    
    local gameOverUI = playerGui:FindFirstChild("GameOverUI")
    if gameOverUI then
        gameOverUI:Destroy()
        print("已移除游戏结束UI")
    end
end
```

#### `AddTeleportCountdownToGameOverUI()` - 在游戏结束UI上添加传送倒计时
**位置**: 第1655-1685行
```lua
function PlayerDownedClient:AddTeleportCountdownToGameOverUI(data)
    -- 查找游戏结束UI中的倒计时文本
    local countdownText = mainFrame:FindFirstChild("CountdownText")
    if countdownText then
        -- 更新倒计时文本为传送倒计时
        countdownText.Text = "10 秒后传送回初始场景"
        countdownText.TextColor3 = Color3.new(1, 0.8, 0) -- 金色
        
        -- 启动传送倒计时更新
        self:StartTeleportCountdownUpdate(countdownText, data.countdownTime or 10)
    end
end
```

#### `StartTeleportCountdownUpdate()` - 启动传送倒计时更新
**位置**: 第1687-1715行
```lua
function PlayerDownedClient:StartTeleportCountdownUpdate(countdownText, totalTime)
    -- 实时更新传送倒计时显示
    self.TeleportCountdownConnection = RunService.Heartbeat:Connect(function()
        local elapsed = tick() - startTime
        local remaining = math.max(0, totalTime - elapsed)
        local remainingSeconds = math.ceil(remaining)
        
        if remainingSeconds > 0 then
            countdownText.Text = remainingSeconds .. " 秒后传送回初始场景"
        else
            countdownText.Text = "正在传送..."
        end
    end)
end
```

### 4. 优化UI切换逻辑

#### 修改 `OnGameOver()` 函数
**位置**: 第1455-1461行
```lua
-- 显示游戏结束UI（如果尚未显示）
if not self:IsGameOverUIVisible() then
    print("准备显示游戏结束UI")
    self:ShowGameOverUI()
else
    print("游戏结束UI已显示，跳过重复创建")
end
```

#### 修改 `SetupCharacter()` 函数
**位置**: 第367-383行
```lua
-- 停止传送倒计时连接（如果有的话）
if self.TeleportCountdownConnection then
    self.TeleportCountdownConnection:Disconnect()
    self.TeleportCountdownConnection = nil
end

-- 移除游戏结束UI（如果有的话）
self:RemoveGameOverUI()
```

## 🔄 修复后的流程

### 新的完整流程：
1. **玩家濒死** → 显示濒死UI + 自我复活倒计时（10秒）
2. **自我复活倒计时结束** → **显示游戏结束UI** → 通知服务端
3. **服务端检查后发送AllPlayersDowned事件** → **在游戏结束UI上显示传送倒计时**
4. **传送倒计时结束** → 执行传送

### 关键改进：
- ✅ 自我复活超时后先显示游戏结束UI
- ✅ 传送倒计时在游戏结束UI上显示，而不是独立UI
- ✅ 避免UI重复创建和显示冲突
- ✅ 正确的状态清理和连接管理

## 🧪 测试方法

### 1. 使用测试脚本
运行 `Scripts\Test\TestPlayerDownedFlow.lua` 进行自动化测试

### 2. 手动测试步骤
1. 在Studio中进入游戏
2. 将角色血量设为0触发濒死
3. 观察自我复活倒计时（10秒）
4. 等待倒计时结束，应该看到游戏结束UI
5. 观察游戏结束UI上的传送倒计时（10秒）

### 3. 预期结果
- 濒死UI正常显示
- 自我复活倒计时正常工作
- 超时后显示游戏结束UI
- 游戏结束UI上显示传送倒计时
- 倒计时文本变为金色并正常递减

## 📝 注意事项

1. **服务端逻辑未修改** - 只修改了客户端UI显示逻辑
2. **保持向后兼容** - 现有的传送系统仍然正常工作
3. **状态管理** - 添加了适当的连接清理和状态重置
4. **错误处理** - 添加了UI存在性检查和错误处理

## 🔧 如果需要进一步调整

如果您需要修改倒计时时间或UI样式，可以调整以下位置：
- 自我复活倒计时时间：`PlayerDownedClient.SelfReviveTime`（第40行）
- 传送倒计时时间：服务端 `GameEndTeleportService.COUNTDOWN_TIME`
- UI样式：`ShowGameOverUI()` 和 `AddTeleportCountdownToGameOverUI()` 函数中的UI创建代码
