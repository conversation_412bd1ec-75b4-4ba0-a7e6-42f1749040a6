local replicatedStorage=game:GetService("ReplicatedStorage")

local NotifyService = {}


NotifyService.remoteFolder=replicatedStorage.Remotes

NotifyService.EventData={
	SayHello 			= "SayHello",
	WeaponSkillCooldown = "WeaponSkillCooldown",	-- 添加武器技能冷却事件
	WeaponEquipped 		= "WeaponEquipped", 		-- 添加武器装备事件
	PlayAttackEffect 	= "PlayAttackEffect", 		-- 添加攻击特效同步事件
	DeathBoxCreated 	= "DeathBoxCreated", 		-- 添加死亡盒子创建事件
	DeathBoxRemoved		= "DeathBoxRemoved", 		-- 添加死亡盒子移除事件
	ShowMessage 		= "ShowMessage",  			-- 添加消息提示事件
	BulletCreated 		= "BulletCreated", 			-- 添加子弹创建事件
	BulletHit 			= "BulletHit", 				-- 添加子弹命中事件
	AmmoUpdated 		= "AmmoUpdated", 			-- 添加弹药更新事件
	ReloadStarted 		= "ReloadStarted",		    -- 添加开始换弹事件
	ReloadFinished 		= "ReloadFinished", 		-- 添加换弹完成事件
	AmmoInventoryUpdate = "AmmoInventoryUpdate", 	-- 添加弹药库更新事件
	AmmoInventorySync 	= "AmmoInventorySync", 		-- 添加弹药库同步事件
	ReloadCancelled 	= "ReloadCancelled", 		-- 添加换弹取消事件
	PlayerDowned        = "PlayerDowned",           -- 玩家倒地事件
	PlayerDownedBroadcast = "PlayerDownedBroadcast", -- 广播玩家倒地事件
	PlayerRevived       = "PlayerRevived",          -- 玩家复活事件
	PlayerRevivedBroadcast = "PlayerRevivedBroadcast", -- 广播玩家复活事件
	PickupDenied       = "PickupDenied",          	-- 拾取被拒绝事件
	RestoreItemUIData   = "RestoreItemUIData",      -- 恢复ItemUI背包数据事件
	BandageUseStarted   = "BandageUseStarted",      -- 绷带使用开始事件
	BandageUseEnded     = "BandageUseEnded",        -- 绷带使用结束事件

	ItemGrabbed 		= "ItemGrabbed",			-- 物品抓取
	ItemMoved 			= "ItemMoved",	 			-- 物品移动
	ItemRotated 		= "ItemRotated", 			-- 物品旋转
	ItemReleased 		= "ItemReleased",			-- 物品释放
	ItemDestroyed 		= "ItemDestroyed",			-- 物品销毁
	ItemWelded 			= "ItemWelded",      		-- 添加焊接事件
	ItemUnwelded 		= "ItemUnwelded", 			-- 添加解除焊接事件
	CreateMoneyModel 	= "CreateMoneyModel",    	-- 创建货币模型事件
	MoneyModelDestroyed = "MoneyModelDestroyed", 	-- 货币模型销毁事件
	ItemTill			= "ItemTill",				-- 物品购买事件
	AddCashier      	= "AddCashier",				-- 添加到收银台
	RemoveCashier		= "RemoveCashier",			-- 移除收银台
	ItemDrop            = "ItemDrop",				-- 物品丢弃生成
	
	UpdateState			= "UpdateState",			-- 客户端更新属性
	UpdateEquipUI		= "UpdateEquipUI",			-- 更新装备UI
	SetEquip			= "SetEquip",
	ToolEquip			= "ToolEquip",				-- 武器同步	
	ToolRemove			= "ToolRemove",			-- 武器移除
	SwithProfession     = "SwithProfession",
	SetFuel				= "SetFuel",
	UpdateCurrencyUI    = "UpdateCurrencyUI",
	SetCurrency 		= "SetCurrency",
	EquipAnchored       = "EquipAnchored",
	PlayerSelfRevive 	= "PlayerSelfRevive", 			-- 添加玩家自我复活事件
	CheckGameEndCondition = "CheckGameEndCondition", 	-- 添加检查游戏结束条件事件
	GameOver 			= "GameOver", 					-- 添加游戏结束事件
	AllPlayersDowned 	= "AllPlayersDowned", 			-- 所有玩家濒死事件
	
	TeleportToInitialStart = "TeleportToInitialStart", -- 开始传送回初始场景
	TeleportToInitialFailed = "TeleportToInitialFailed", -- 传送失败
	RequestTeleportToInitial = "RequestTeleportToInitial", -- 请求传送回初始场景
	CheckGameEndCondition = "CheckGameEndCondition", -- 检查游戏结束条件
	PlayerSelfRevive = "PlayerSelfRevive", -- 玩家自我复活
}

-- 日志函数
local function log(message)
	--print("[Notify] " .. tostring(message))
end
--服务端初始化生成remoteevent
function NotifyService.GenerateRemote()
	--添加初始的Remote
	local remotes=replicatedStorage.Remotes:GetChildren()
	for k, v in pairs(remotes) do
		if not NotifyService[v.Name] then
			NotifyService[v.Name]=v
		end
	end

	for k,v in pairs(NotifyService.EventData) do
		if NotifyService[k] then
			--print("已经存在名为",k,"的RemoteEvent,请定义其他的RemoteEvent.")
		else
			local remoteEvent=Instance.new("RemoteEvent")
			remoteEvent.Name=k
			remoteEvent.Parent=NotifyService.remoteFolder
			NotifyService[k]=remoteEvent
		end
	end
end


function NotifyService.FireClient(remoteEventName,player,message)

	local remoteEvent=NotifyService[remoteEventName]
	if	remoteEvent then
		remoteEvent:FireClient(player,message)
	end
end

function NotifyService.FireServer(remoteEventName,message)
	log("向服务端发送事件: " .. remoteEventName)
	local remoteEvent=NotifyService[remoteEventName]
	if	remoteEvent then
		remoteEvent:FireServer(message)
	else
		warn("RemoteEvent不存在:", remoteEventName)
	end
end

function NotifyService.FireAllClient(remoteEventName,message)
	log("触发所有客户端事件: " .. remoteEventName)
	local remoteEvent=NotifyService[remoteEventName]
	if	remoteEvent then
		remoteEvent:FireAllClients(message)
	else
		print("RemoteEventName is not exist:",remoteEventName)
	end
end

--客户端注册事件
function NotifyService.RegisterClientEvent(remoteEventName,callback)
	log("注册客户端事件: " .. remoteEventName)
	local remoteEvent=NotifyService.remoteFolder:WaitForChild(remoteEventName)
	if remoteEvent then
		remoteEvent.OnClientEvent:Connect(callback)
	end
end

--服务端注册事件
function NotifyService.RegisterServerEvent(remoteEventName,callback)
	log("注册服务端事件: " .. remoteEventName)
	local remoteEvent=NotifyService[remoteEventName]
	if remoteEvent then
		remoteEvent.OnServerEvent:Connect(callback)
	else
		warn("RemoteEvent不存在:", remoteEventName)
	end
end

-- 向除指定玩家外的所有客户端发送消息
function NotifyService.FireAllExceptClient(remoteEventName, exceptPlayer, message)
	local remoteEvent=NotifyService[remoteEventName]
	if	remoteEvent then
		for _, player in pairs(game:GetService("Players"):GetPlayers()) do
			if player ~= exceptPlayer then
				remoteEvent:FireClient(player, message)
			end
		end
	else
		print("RemoteEventName is not exist:", remoteEventName)
	end
end

return NotifyService