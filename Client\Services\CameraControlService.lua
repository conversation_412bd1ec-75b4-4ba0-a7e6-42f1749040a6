--[[
摄像机控制服务
负责管理玩家的视角控制，包括第一视角和第三视角的切换
以及远程武器跟随鼠标转动功能
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- 引入通知服务
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local CameraControlService = {}

-- 获取本地玩家
local player = Players.LocalPlayer

-- 摄像机设置常量
local FIRST_PERSON_ZOOM = 0.5  -- 第一视角缩放距离
local THIRD_PERSON_ZOOM = 15   -- 第三视角缩放距离

-- 状态变量
local isFirstPerson = true  -- 当前是否为第一视角
local originalCameraSettings = {}  -- 保存原始摄像机设置
local lastCameraChangeTime = 0  -- 上次相机切换时间
local CAMERA_CHANGE_COOLDOWN = 1  -- 相机切换冷却时间（秒）

-- 武器跟随系统变量
local weaponFollowActive = false  -- 武器跟随是否激活
local currentWeaponTool = nil     -- 当前跟随的武器工具
local weaponFollowConnection = nil -- 武器跟随的连接
local weaponFollowPart = nil      -- 武器跟随控制部件
local weaponWeldConstraint = nil  -- 武器焊接约束

-- 武器跟随配置（只控制上下跟随，左右跟随使用游戏默认）
local WEAPON_FOLLOW_CONFIG = {
	weaponOffset = {
		forward = 1.5,  -- 向前偏移（减少，避免武器太远）
		right = 0.4,    -- 向右偏移（减少）
		up = -0.5       -- 向上偏移（降低，更符合持枪姿势）
	},
	followSmoothing = 0.25,  -- 位置平滑度
	rotationSmoothing = 0.2, -- 旋转平滑度（保持原值）
	smoothness = 0.8,        -- C1偏移平滑度
	rotationLimits = {
		maxPitch = 75,  -- 最大俯仰角
		minPitch = -75  -- 最小俯仰角
		-- 移除偏航角限制，让游戏默认处理左右跟随
	}
}

-- 初始化服务
function CameraControlService:Initialize()
	print("CameraControlService 初始化")

	-- 等待玩家加载
	if not player then
		player = Players.PlayerAdded:Wait()
	end

	-- 保存原始摄像机设置
	self:SaveOriginalCameraSettings()

	-- 设置初始第一视角
	self:SetFirstPersonView()

	-- 监听濒死状态变化
	self:SetupDownedStateListener()

	-- 监听角色重生
	player.CharacterAdded:Connect(function(character)
		-- 角色重生时重新设置第一视角
		wait(1) -- 等待角色完全加载
		self:SetFirstPersonView()
	end)

	print("CameraControlService 初始化完成")
end

-- 保存原始摄像机设置
function CameraControlService:SaveOriginalCameraSettings()
	if player then
		originalCameraSettings.CameraMinZoomDistance = player.CameraMinZoomDistance
		originalCameraSettings.CameraMaxZoomDistance = player.CameraMaxZoomDistance
		originalCameraSettings.CameraMode = player.CameraMode

		print("已保存原始摄像机设置:")
		print("  MinZoom:", originalCameraSettings.CameraMinZoomDistance)
		print("  MaxZoom:", originalCameraSettings.CameraMaxZoomDistance)
		print("  CameraMode:", originalCameraSettings.CameraMode)
	end
end

-- 设置第一视角
function CameraControlService:SetFirstPersonView()
	if not player then return end

	-- 设置摄像机缩放距离为相同值，强制第一视角
	player.CameraMinZoomDistance = FIRST_PERSON_ZOOM
	player.CameraMaxZoomDistance = FIRST_PERSON_ZOOM
	player.CameraMode = Enum.CameraMode.Classic

	isFirstPerson = true

	print("已设置第一视角 (缩放距离: " .. FIRST_PERSON_ZOOM .. ")")
end

-- 设置第三视角
function CameraControlService:SetThirdPersonView()
	if not player then return end

	-- 设置摄像机缩放距离允许第三视角
	player.CameraMinZoomDistance = THIRD_PERSON_ZOOM
	player.CameraMaxZoomDistance = THIRD_PERSON_ZOOM
	player.CameraMode = Enum.CameraMode.Classic

	isFirstPerson = false

	-- 切换到第三视角时停止武器跟随
	if weaponFollowActive then
		self:StopWeaponCameraFollow()
		print("切换到第三视角，已停止武器跟随")
	end

	print("已设置第三视角 (缩放距离: 5-" .. THIRD_PERSON_ZOOM .. ")")
end

-- 恢复原始摄像机设置
function CameraControlService:RestoreOriginalCameraSettings()
	if not player or not originalCameraSettings.CameraMinZoomDistance then return end

	player.CameraMinZoomDistance = originalCameraSettings.CameraMinZoomDistance
	player.CameraMaxZoomDistance = originalCameraSettings.CameraMaxZoomDistance
	player.CameraMode = originalCameraSettings.CameraMode

	print("已恢复原始摄像机设置")
end

-- 设置濒死状态监听
function CameraControlService:SetupDownedStateListener()
	-- 监听濒死状态事件
	NotifyService.RegisterClientEvent("PlayerDowned", function(data)
		print("收到濒死状态事件，切换到第三视角")
		self:SetThirdPersonView()
	end)

	-- 监听复活事件
	NotifyService.RegisterClientEvent("PlayerRevived", function(data)
		print("收到复活事件，切换到第一视角")
		self:SetFirstPersonView()
	end)

	-- 监听玩家属性变化（备用方案，添加防抖机制）
	if player then
		player.AttributeChanged:Connect(function(attributeName)
			if attributeName == "IsDowned" then
				-- 防抖机制：避免频繁切换相机导致闪烁
				local currentTime = tick()
				if currentTime - lastCameraChangeTime < CAMERA_CHANGE_COOLDOWN then
					print("相机切换冷却中，跳过此次切换")
					return
				end

				local isDowned = player:GetAttribute("IsDowned")
				if isDowned then
					print("检测到濒死属性变化，切换到第三视角")
					self:SetThirdPersonView()
					lastCameraChangeTime = currentTime
				else
					print("检测到复活属性变化，切换到第一视角")
					self:SetFirstPersonView()
					lastCameraChangeTime = currentTime
				end
			end
		end)
	end
end

-- 获取当前视角状态
function CameraControlService:IsFirstPerson()
	return isFirstPerson
end

-- 手动切换视角（调试用）
function CameraControlService:ToggleView()
	if isFirstPerson then
		self:SetThirdPersonView()
	else
		self:SetFirstPersonView()
	end
end

-- ========== 武器跟随系统 ==========

-- 启动武器跟随功能（安全版本，避免瞬移）
function CameraControlService:StartWeaponCameraFollow(weaponTool)
	-- 验证输入参数
	if not weaponTool or not weaponTool:IsA("Tool") then
		print("❌ 无效的武器工具")
		return false
	end

	-- 检查是否已经在跟随状态
	if weaponFollowActive then
		print("⚠️ 武器跟随已激活，先停止当前跟随")
		self:StopWeaponCameraFollow()
	end

	-- 检查是否为第一人称视角
	if not isFirstPerson then
		print("⚠️ 只有在第一人称视角下才能启动武器跟随")
		return false
	end

	-- 获取武器Handle
	local handle = weaponTool:FindFirstChild("Handle")
	if not handle or not handle:IsA("BasePart") then
		print("❌ 武器缺少有效的Handle")
		return false
	end

	-- 获取角色和相机
	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then
		print("❌ 角色或HumanoidRootPart不存在")
		return false
	end

	local camera = workspace.CurrentCamera
	if not camera then
		print("❌ 相机不存在")
		return false
	end

	print("🎯 启动武器跟随: " .. weaponTool.Name)

	-- 使用延迟初始化避免瞬移
	return self:SafeInitializeWeaponFollow(weaponTool, handle, camera)
end

-- 安全初始化武器跟随（改进的稳定方案）
function CameraControlService:SafeInitializeWeaponFollow(weaponTool, handle, camera)
	-- 记录Handle的原始状态
	local originalHandleCFrame = handle.CFrame
	local originalAnchored = handle.Anchored
	local originalCanCollide = handle.CanCollide

	print("🔧 原始Handle状态 - 位置: " .. tostring(handle.Position) .. ", 锚定: " .. tostring(originalAnchored))

	-- 新方案：查找任何可用的连接方式，优先级：Motor6D > Weld > 创建新连接
	local weaponConnection = nil
	local connectionType = nil
	local originalOffset = nil

	-- 方法1：查找Tool系统的Motor6D连接
	local character = player.Character
	if character then
		for _, part in pairs(character:GetDescendants()) do
			if part:IsA("Motor6D") and (part.Part1 == handle or part.Part0 == handle) then
				weaponConnection = part
				connectionType = "Motor6D"
				originalOffset = part.C1
				print("🔍 找到Tool Motor6D: " .. part.Name)
				break
			end
		end
	end

	-- 方法2：如果没有Motor6D，查找Weld连接
	if not weaponConnection and character then
		for _, part in pairs(character:GetDescendants()) do
			if part:IsA("Weld") and (part.Part1 == handle or part.Part0 == handle) then
				weaponConnection = part
				connectionType = "Weld"
				originalOffset = part.C1
				print("🔍 找到Weld连接: " .. part.Name)
				break
			end
		end
	end

	-- 方法3：如果都没有，创建自定义Weld连接
	if not weaponConnection and character then
		local rightHand = character:FindFirstChild("Right Arm") or character:FindFirstChild("RightHand")
		if rightHand then
			print("🔧 创建自定义Weld连接...")

			-- 创建新的Weld
			local weld = Instance.new("Weld")
			weld.Name = "CustomWeaponWeld"
			weld.Part0 = rightHand
			weld.Part1 = handle

			-- 计算初始偏移
			local offset = rightHand.CFrame:ToObjectSpace(handle.CFrame)
			weld.C0 = CFrame.new()
			weld.C1 = offset

			weld.Parent = rightHand

			weaponConnection = weld
			connectionType = "CustomWeld"
			originalOffset = offset

			print("✅ 自定义Weld连接已创建")
		end
	end

	if not weaponConnection then
		print("❌ 无法找到或创建武器连接，武器跟随初始化失败")
		return false
	end

	-- 保存控制对象到全局变量
	weaponFollowPart = weaponConnection
	weaponWeldConstraint = originalOffset  -- 保存原始偏移

	-- 保存状态并启动跟随
	weaponFollowActive = true
	currentWeaponTool = weaponTool

	print("🔧 武器跟随初始化完成 - 使用" .. connectionType .. "方案")
	print("🔧 连接: " .. weaponConnection.Name .. ", 原始偏移: " .. tostring(originalOffset))

	-- 启动跟随更新循环
	weaponFollowConnection = RunService.Heartbeat:Connect(function()
		self:UpdateWeaponFollow()
	end)

	print("✅ 武器跟随已安全启动（" .. connectionType .. "方案）")
	return true
end

-- 停止武器跟随功能（支持多种连接类型）
function CameraControlService:StopWeaponCameraFollow()
	if not weaponFollowActive then
		return
	end

	print("🛑 停止武器跟随")

	-- 断开更新连接
	if weaponFollowConnection then
		weaponFollowConnection:Disconnect()
		weaponFollowConnection = nil
	end

	-- 恢复原始偏移
	if weaponFollowPart and weaponWeldConstraint then
		if weaponFollowPart:IsA("Motor6D") or weaponFollowPart:IsA("Weld") then
			weaponFollowPart.C1 = weaponWeldConstraint  -- 恢复原始C1
			print("🔧 已恢复原始偏移: " .. weaponFollowPart.ClassName)
		end

		-- 如果是自定义创建的Weld，可以选择删除它
		if weaponFollowPart.Name == "CustomWeaponWeld" then
			print("🗑️ 删除自定义Weld连接")
			weaponFollowPart:Destroy()
		end
	end

	-- 重置状态
	weaponFollowActive = false
	currentWeaponTool = nil
	weaponFollowPart = nil
	weaponWeldConstraint = nil

	print("✅ 武器跟随已停止")
end

-- 检查武器跟随是否激活
function CameraControlService:IsWeaponFollowActive()
	return weaponFollowActive
end

-- 更新武器跟随（支持多种连接类型）
function CameraControlService:UpdateWeaponFollow()
	-- 安全检查
	if not weaponFollowActive or not weaponFollowPart or not currentWeaponTool then
		return
	end

	-- 检查武器是否仍然存在
	if not currentWeaponTool.Parent then
		print("⚠️ 武器已被移除，停止跟随")
		self:StopWeaponCameraFollow()
		return
	end

	-- 检查是否仍为第一人称
	if not isFirstPerson then
		print("⚠️ 切换到第三人称，停止武器跟随")
		self:StopWeaponCameraFollow()
		return
	end

	-- 检查连接是否仍然有效
	if not weaponFollowPart.Parent then
		print("⚠️ 武器连接丢失，停止跟随")
		self:StopWeaponCameraFollow()
		return
	end

	-- 获取相机
	local camera = workspace.CurrentCamera
	if not camera then
		return
	end

	-- 只计算相机的俯仰角（上下跟随），左右跟随使用游戏默认功能
	local cameraDirection = camera.CFrame.LookVector
	local cameraPitch = math.asin(-cameraDirection.Y)  -- 俯仰角（弧度）

	-- 应用俯仰角限制
	local config = WEAPON_FOLLOW_CONFIG.rotationLimits
	local limitedPitch = math.clamp(math.deg(cameraPitch), config.minPitch, config.maxPitch)

	-- 转换回弧度
	limitedPitch = math.rad(limitedPitch)

	-- 计算新的偏移（基于原始偏移 + 只有俯仰角旋转）
	local originalOffset = weaponWeldConstraint  -- 原始偏移

	-- 只创建俯仰角旋转（上下跟随），不控制偏航角（左右跟随）
	local pitchRotation = CFrame.Angles(limitedPitch, 0, 0)

	-- 只使用俯仰角旋转，让游戏默认处理左右跟随
	local cameraRotation = pitchRotation

	-- 根据连接类型应用旋转
	if weaponFollowPart:IsA("Motor6D") or weaponFollowPart:IsA("Weld") then
		-- 对于Motor6D和Weld，使用C1偏移
		local targetC1 = originalOffset * cameraRotation

		-- 使用平滑插值避免抖动
		local currentC1 = weaponFollowPart.C1
		local smoothness = WEAPON_FOLLOW_CONFIG.smoothness or 0.8
		weaponFollowPart.C1 = currentC1:Lerp(targetC1, smoothness)
	end

	-- 调试输出（每60帧输出一次）
	if tick() % 1 < 0.016 then  -- 大约每秒输出一次
		local handle = currentWeaponTool:FindFirstChild("Handle")
		if handle then
			local weaponDirection = handle.CFrame.LookVector
			local weaponPitch = math.deg(math.asin(-weaponDirection.Y))
			local cameraPitchDeg = math.deg(cameraPitch)
			print(string.format("🎯 武器跟随更新 - 相机俯仰: %.1f°, 武器俯仰: %.1f°, Motor6D: %s",
				cameraPitchDeg, weaponPitch, weaponFollowPart.Name))
		end
	end
end

-- 计算武器位置和旋转（修复版本）
function CameraControlService:CalculateWeaponPosition(cameraCFrame, useCurrentHandlePosition)
	-- 获取相机的方向向量
	local cameraDirection = cameraCFrame.LookVector
	local cameraUp = cameraCFrame.UpVector
	local cameraRight = cameraCFrame.RightVector

	-- 计算俯仰角（上下转动）
	local pitch = math.asin(-cameraDirection.Y)
	pitch = math.deg(pitch)

	-- 应用俯仰角限制
	local limitedPitch = math.clamp(pitch, WEAPON_FOLLOW_CONFIG.rotationLimits.minPitch, WEAPON_FOLLOW_CONFIG.rotationLimits.maxPitch)

	-- 正确计算限制后的方向向量（保持水平分量的比例）
	local limitedPitchRad = math.rad(limitedPitch)
	local horizontalDirection = Vector3.new(cameraDirection.X, 0, cameraDirection.Z).Unit
	local limitedDirection = Vector3.new(
		horizontalDirection.X * math.cos(limitedPitchRad),
		-math.sin(limitedPitchRad),
		horizontalDirection.Z * math.cos(limitedPitchRad)
	).Unit

	-- 计算武器基础位置
	local basePosition
	if useCurrentHandlePosition and currentWeaponTool then
		-- 初始化时使用Handle的当前位置作为基础，避免瞬移
		local handle = currentWeaponTool:FindFirstChild("Handle")
		if handle then
			basePosition = handle.Position
		else
			basePosition = cameraCFrame.Position
				+ limitedDirection * WEAPON_FOLLOW_CONFIG.weaponOffset.forward
				+ cameraRight * WEAPON_FOLLOW_CONFIG.weaponOffset.right
				+ cameraUp * WEAPON_FOLLOW_CONFIG.weaponOffset.up
		end
	else
		-- 正常跟随时使用相机位置计算
		basePosition = cameraCFrame.Position
			+ limitedDirection * WEAPON_FOLLOW_CONFIG.weaponOffset.forward
			+ cameraRight * WEAPON_FOLLOW_CONFIG.weaponOffset.right
			+ cameraUp * WEAPON_FOLLOW_CONFIG.weaponOffset.up
	end

	-- 构建武器的旋转CFrame（使用修正后的方向向量）
	local weaponCFrame = CFrame.lookAt(basePosition, basePosition + limitedDirection, cameraUp)

	return weaponCFrame
end

-- 设置武器跟随配置
function CameraControlService:SetWeaponFollowConfig(config)
	if config.weaponOffset then
		for key, value in pairs(config.weaponOffset) do
			WEAPON_FOLLOW_CONFIG.weaponOffset[key] = value
		end
	end

	if config.followSmoothing then
		WEAPON_FOLLOW_CONFIG.followSmoothing = config.followSmoothing
	end

	if config.rotationSmoothing then
		WEAPON_FOLLOW_CONFIG.rotationSmoothing = config.rotationSmoothing
	end

	if config.rotationLimits then
		for key, value in pairs(config.rotationLimits) do
			WEAPON_FOLLOW_CONFIG.rotationLimits[key] = value
		end
	end

	print("✅ 武器跟随配置已更新")
end

-- 获取当前武器跟随配置
function CameraControlService:GetWeaponFollowConfig()
	return WEAPON_FOLLOW_CONFIG
end

return CameraControlService
