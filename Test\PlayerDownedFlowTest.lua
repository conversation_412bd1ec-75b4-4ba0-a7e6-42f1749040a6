--!strict
-- 濒死流程测试脚本
-- 用于测试从濒死到自我复活超时到结束UI到传送的完整流程

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local PlayerDownedFlowTest = {}

-- 测试配置
local TEST_CONFIG = {
	SELF_REVIVE_TIMEOUT = 10, -- 自我复活超时时间（秒）
	TELEPORT_COUNTDOWN = 10,  -- 传送倒计时时间（秒）
	TEST_DELAY = 1           -- 测试步骤间的延迟（秒）
}

-- 测试状态
local testState = {
	isRunning = false,
	currentStep = 0,
	startTime = 0,
	results = {}
}

-- 获取必要的服务
local function getServices()
	local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
	local PlayerDownedClient = require(ReplicatedStorage.Scripts.Client.Services.PlayerDownedClient)
	
	return PlayerDownedService, PlayerDownedClient
end

-- 记录测试结果
local function logResult(step, description, success, details)
	local result = {
		step = step,
		description = description,
		success = success,
		details = details or "",
		timestamp = tick() - testState.startTime
	}
	
	table.insert(testState.results, result)
	
	local status = success and "✅" or "❌"
	print(string.format("[%.2fs] %s 步骤%d: %s - %s", 
		result.timestamp, status, step, description, details))
end

-- 检查UI是否存在
local function checkUIExists(uiName)
	local player = Players.LocalPlayer
	if not player then return false end
	
	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then return false end
	
	return playerGui:FindFirstChild(uiName) ~= nil
end

-- 检查玩家是否处于濒死状态
local function isPlayerDowned()
	local player = Players.LocalPlayer
	if not player then return false end
	
	return player:GetAttribute("IsDowned") == true
end

-- 步骤1: 模拟玩家濒死
local function step1_SimulatePlayerDowned()
	print("\n=== 步骤1: 模拟玩家濒死 ===")
	
	local player = Players.LocalPlayer
	if not player or not player.Character then
		logResult(1, "模拟玩家濒死", false, "玩家或角色不存在")
		return false
	end
	
	local humanoid = player.Character:FindFirstChildOfClass("Humanoid")
	if not humanoid then
		logResult(1, "模拟玩家濒死", false, "Humanoid不存在")
		return false
	end
	
	-- 设置血量为0触发濒死
	humanoid.Health = 0
	
	-- 等待濒死状态生效
	wait(1)
	
	-- 检查是否成功进入濒死状态
	local isDowned = isPlayerDowned()
	local hasDownedUI = checkUIExists("DownedUI")
	
	logResult(1, "模拟玩家濒死", isDowned and hasDownedUI, 
		string.format("濒死状态: %s, 濒死UI: %s", tostring(isDowned), tostring(hasDownedUI)))
	
	return isDowned and hasDownedUI
end

-- 步骤2: 等待自我复活倒计时完成
local function step2_WaitSelfReviveTimeout()
	print("\n=== 步骤2: 等待自我复活倒计时完成 ===")
	
	local startTime = tick()
	local timeoutReached = false
	local gameOverUIShown = false
	
	-- 监控倒计时过程
	while tick() - startTime < TEST_CONFIG.SELF_REVIVE_TIMEOUT + 2 do
		local elapsed = tick() - startTime
		
		-- 检查是否到达超时时间
		if elapsed >= TEST_CONFIG.SELF_REVIVE_TIMEOUT and not timeoutReached then
			timeoutReached = true
			print("自我复活倒计时应该已超时")
		end
		
		-- 检查游戏结束UI是否显示
		if timeoutReached and not gameOverUIShown then
			if checkUIExists("GameOverUI") then
				gameOverUIShown = true
				print("游戏结束UI已显示")
			end
		end
		
		wait(0.5)
	end
	
	logResult(2, "等待自我复活倒计时完成", timeoutReached and gameOverUIShown,
		string.format("超时: %s, 游戏结束UI: %s", tostring(timeoutReached), tostring(gameOverUIShown)))
	
	return timeoutReached and gameOverUIShown
end

-- 步骤3: 检查传送倒计时是否在游戏结束UI上显示
local function step3_CheckTeleportCountdown()
	print("\n=== 步骤3: 检查传送倒计时 ===")
	
	-- 等待服务端处理和传送倒计时启动
	wait(2)
	
	local hasGameOverUI = checkUIExists("GameOverUI")
	local hasTeleportCountdown = false
	
	if hasGameOverUI then
		local player = Players.LocalPlayer
		local playerGui = player:FindFirstChild("PlayerGui")
		local gameOverUI = playerGui:FindFirstChild("GameOverUI")
		
		if gameOverUI then
			local mainFrame = gameOverUI:FindFirstChild("MainFrame")
			if mainFrame then
				local countdownText = mainFrame:FindFirstChild("CountdownText")
				if countdownText and string.find(countdownText.Text, "传送") then
					hasTeleportCountdown = true
				end
			end
		end
	end
	
	logResult(3, "检查传送倒计时", hasGameOverUI and hasTeleportCountdown,
		string.format("游戏结束UI: %s, 传送倒计时: %s", tostring(hasGameOverUI), tostring(hasTeleportCountdown)))
	
	return hasGameOverUI and hasTeleportCountdown
end

-- 步骤4: 监控传送倒计时完成
local function step4_MonitorTeleportCountdown()
	print("\n=== 步骤4: 监控传送倒计时完成 ===")
	
	local startTime = tick()
	local countdownCompleted = false
	
	-- 监控传送倒计时
	while tick() - startTime < TEST_CONFIG.TELEPORT_COUNTDOWN + 3 do
		local player = Players.LocalPlayer
		local playerGui = player:FindFirstChild("PlayerGui")
		local gameOverUI = playerGui and playerGui:FindFirstChild("GameOverUI")
		
		if gameOverUI then
			local mainFrame = gameOverUI:FindFirstChild("MainFrame")
			if mainFrame then
				local countdownText = mainFrame:FindFirstChild("CountdownText")
				if countdownText then
					local text = countdownText.Text
					print("当前倒计时文本:", text)
					
					if string.find(text, "正在传送") or string.find(text, "0 秒") then
						countdownCompleted = true
						break
					end
				end
			end
		end
		
		wait(1)
	end
	
	logResult(4, "监控传送倒计时完成", countdownCompleted,
		countdownCompleted and "传送倒计时已完成" or "传送倒计时未完成")
	
	return countdownCompleted
end

-- 运行完整测试
function PlayerDownedFlowTest.runFullTest()
	if testState.isRunning then
		warn("测试已在运行中")
		return
	end
	
	print("🚀 开始濒死流程完整测试")
	print("=" * 50)
	
	testState.isRunning = true
	testState.currentStep = 0
	testState.startTime = tick()
	testState.results = {}
	
	local success = true
	
	-- 执行测试步骤
	success = success and step1_SimulatePlayerDowned()
	if success then
		success = success and step2_WaitSelfReviveTimeout()
	end
	if success then
		success = success and step3_CheckTeleportCountdown()
	end
	if success then
		success = success and step4_MonitorTeleportCountdown()
	end
	
	-- 输出测试结果
	print("\n" .. "=" * 50)
	print("📊 测试结果汇总")
	print("=" * 50)
	
	local passedSteps = 0
	for _, result in ipairs(testState.results) do
		if result.success then
			passedSteps = passedSteps + 1
		end
	end
	
	print(string.format("总步骤: %d, 通过: %d, 失败: %d", 
		#testState.results, passedSteps, #testState.results - passedSteps))
	
	if success then
		print("🎉 所有测试步骤通过！濒死流程工作正常")
	else
		print("⚠️ 部分测试步骤失败，请检查流程")
	end
	
	testState.isRunning = false
	return success
end

-- 快速测试（仅测试UI显示）
function PlayerDownedFlowTest.runQuickTest()
	print("⚡ 开始快速UI测试")
	
	-- 直接触发濒死状态
	local PlayerDownedService, PlayerDownedClient = getServices()
	local player = Players.LocalPlayer
	
	if player and player.Character then
		-- 模拟濒死
		PlayerDownedClient:OnPlayerDowned({})
		wait(1)
		
		-- 检查濒死UI
		local hasDownedUI = checkUIExists("DownedUI")
		print("濒死UI显示:", hasDownedUI)
		
		-- 模拟自我复活超时
		PlayerDownedClient:OnSelfReviveTimeoutComplete()
		wait(1)
		
		-- 检查游戏结束UI
		local hasGameOverUI = checkUIExists("GameOverUI")
		print("游戏结束UI显示:", hasGameOverUI)
		
		-- 模拟传送倒计时
		PlayerDownedClient:OnAllPlayersDowned({countdownTime = 10})
		wait(1)
		
		print("快速测试完成")
		return hasDownedUI and hasGameOverUI
	else
		warn("无法进行快速测试：玩家或角色不存在")
		return false
	end
end

-- 清理测试状态
function PlayerDownedFlowTest.cleanup()
	print("🧹 清理测试状态")
	
	local player = Players.LocalPlayer
	if player then
		-- 移除测试UI
		local playerGui = player:FindFirstChild("PlayerGui")
		if playerGui then
			local downedUI = playerGui:FindFirstChild("DownedUI")
			if downedUI then downedUI:Destroy() end
			
			local gameOverUI = playerGui:FindFirstChild("GameOverUI")
			if gameOverUI then gameOverUI:Destroy() end
		end
		
		-- 重置玩家状态
		player:SetAttribute("IsDowned", false)
		
		if player.Character then
			local humanoid = player.Character:FindFirstChildOfClass("Humanoid")
			if humanoid then
				humanoid.Health = humanoid.MaxHealth
			end
		end
	end
	
	testState.isRunning = false
	print("测试状态已清理")
end

-- 显示帮助信息
function PlayerDownedFlowTest.showHelp()
	print("🔧 濒死流程测试帮助")
	print("可用的测试函数:")
	print("  runFullTest() - 运行完整流程测试")
	print("  runQuickTest() - 快速UI测试")
	print("  cleanup() - 清理测试状态")
	print("  showHelp() - 显示此帮助信息")
end

return PlayerDownedFlowTest
