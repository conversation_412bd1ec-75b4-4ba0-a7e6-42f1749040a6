--!strict
-- 游戏结束传送功能管理员命令
-- 提供便捷的命令来测试和管理传送功能

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TeleportExecutorService = require(ReplicatedStorage.Scripts.Server.Services.TeleportExecutorService)
local TeleportFixTest = require(ReplicatedStorage.Scripts.Test.TeleportFixTest)
local GameOverTeleportFlowTest = require(ReplicatedStorage.Scripts.Test.GameOverTeleportFlowTest)

local GameEndTeleportCommands = {}

-- 管理员列表（可以根据需要修改）
local ADMIN_USER_IDS = {
	-- 在这里添加管理员的用户ID
	-- 例如: 123456789
}

-- 检查玩家是否为管理员
local function isAdmin(player)
	-- 在Studio中，所有玩家都是管理员
	if game:GetService("RunService"):IsStudio() then
		return true
	end
	
	-- 检查管理员列表
	for _, adminId in ipairs(ADMIN_USER_IDS) do
		if player.UserId == adminId then
			return true
		end
	end
	
	return false
end

-- 发送消息给玩家
local function sendMessage(player, message)
	print("[命令] " .. player.Name .. ": " .. message)
	-- 这里可以添加游戏内消息显示逻辑
end

-- 初始化命令系统
function GameEndTeleportCommands:Initialize()
	print("GameEndTeleportCommands 初始化开始")
	
	-- 监听玩家聊天
	Players.PlayerAdded:Connect(function(player)
		player.Chatted:Connect(function(message)
			self:ProcessCommand(player, message)
		end)
	end)
	
	-- 处理已在服务器的玩家
	for _, player in ipairs(Players:GetPlayers()) do
		player.Chatted:Connect(function(message)
			self:ProcessCommand(player, message)
		end)
	end
	
	print("GameEndTeleportCommands 初始化完成")
end

-- 处理命令
function GameEndTeleportCommands:ProcessCommand(player, message)
	-- 检查是否为命令（以/开头）
	if not message:sub(1, 1) == "/" then
		return
	end
	
	-- 检查管理员权限
	if not isAdmin(player) then
		return
	end
	
	-- 解析命令
	local args = {}
	for word in message:gmatch("%S+") do
		table.insert(args, word)
	end
	
	local command = args[1]:sub(2):lower() -- 移除/并转为小写
	
	-- 执行对应命令
	if command == "teleporttest" then
		self:HandleTeleportTest(player, args)
	elseif command == "flowtest" then
		self:HandleFlowTest(player, args)
	elseif command == "alldown" then
		self:HandleAllDown(player, args)
	elseif command == "reviveall" then
		self:HandleReviveAll(player, args)
	elseif command == "teleportnow" then
		self:HandleTeleportNow(player, args)
	elseif command == "teleporthelp" then
		self:HandleTeleportHelp(player, args)
	end
end

-- 处理传送测试命令
function GameEndTeleportCommands:HandleTeleportTest(player, args)
	local testType = args[2] and args[2]:lower() or "quick"

	sendMessage(player, "开始传送功能测试: " .. testType)

	if testType == "full" then
		-- 运行完整测试
		spawn(function()
			local success = TeleportFixTest.runCompleteFixTest()
			sendMessage(player, "完整测试结果: " .. (success and "成功" or "失败"))
		end)
	elseif testType == "quick" then
		-- 运行快速测试
		spawn(function()
			local success = TeleportFixTest.runQuickTeleportTest()
			sendMessage(player, "快速测试结果: " .. (success and "成功" or "失败"))
		end)
	else
		sendMessage(player, "未知的测试类型: " .. testType)
		sendMessage(player, "可用类型: quick, full")
	end
end

-- 处理流程测试命令
function GameEndTeleportCommands:HandleFlowTest(player, args)
	local testType = args[2] and args[2]:lower() or "quick"

	sendMessage(player, "开始流程测试: " .. testType)

	spawn(function()
		local success = GameOverTeleportFlowTest.runCompleteFlowTest()
		sendMessage(player, "流程测试结果: " .. (success and "成功" or "失败"))
	end)
end

-- 处理所有玩家濒死命令
function GameEndTeleportCommands:HandleAllDown(player, args)
	sendMessage(player, "模拟所有玩家濒死状态")

	spawn(function()
		local success = GameOverTeleportFlowTest.simulateAllPlayersDowned()
		sendMessage(player, "模拟结果: " .. (success and "成功" or "失败"))
	end)
end

-- 处理复活所有玩家命令
function GameEndTeleportCommands:HandleReviveAll(player, args)
	sendMessage(player, "复活所有玩家")
	
	local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
	local revivedCount = 0
	
	for _, targetPlayer in ipairs(Players:GetPlayers()) do
		if PlayerDownedService:IsPlayerDowned(targetPlayer) then
			PlayerDownedService:RevivePlayer(targetPlayer)
			revivedCount = revivedCount + 1
		end
	end
	
	sendMessage(player, "已复活 " .. revivedCount .. " 个玩家")
end

-- 处理立即传送命令
function GameEndTeleportCommands:HandleTeleportNow(player, args)
	sendMessage(player, "立即传送所有玩家回初始场景")

	spawn(function()
		TeleportExecutorService:ManualTeleportAllPlayers("adminCommand")
		sendMessage(player, "传送命令已执行")
	end)
end

-- 处理帮助命令
function GameEndTeleportCommands:HandleTeleportHelp(player, args)
	sendMessage(player, "=== 传送功能管理员命令帮助 ===")
	sendMessage(player, "/teleporttest [quick|full] - 运行传送功能测试")
	sendMessage(player, "/flowtest - 运行游戏流程测试")
	sendMessage(player, "/alldown - 模拟所有玩家濒死")
	sendMessage(player, "/reviveall - 复活所有玩家")
	sendMessage(player, "/teleportnow - 立即传送回初始场景")
	sendMessage(player, "/teleporthelp - 显示此帮助信息")
	sendMessage(player, "=== 注意：这些命令仅限管理员使用 ===")
end

return GameEndTeleportCommands
