--!strict
-- 传输数据管理器
-- 负责处理场景间传输的数据，并与新手盒子系统集成

local TeleportService = game:GetService("TeleportService")
local Players = game:GetService("Players")

-- 类型定义
type Player = Player
type ItemData = {
	id: number,
	quantity: number,
	itemType: number
}
type PlayerItemData = {
	playerName: string,
	occupationId: number,
	items: {ItemData}
}
type TeleportData = {
	-- 新格式字段
	sourcePlace: number?,
	timestamp: number?,
	playerItemData: {[string]: PlayerItemData}?,
	professionPlayer: {[string]: string}?,
	professionState: {[string]: number}?,
	-- 旧格式兼容字段
	job: string?,
	str: string?,
	items: {ItemData}?
}
type DataStatus = "waiting" | "received" | "timeout" | "no_data" | "unknown"

local TeleportDataManager = {}

-- 存储玩家的传输数据
local playerTeleportData: {[number]: TeleportData} = {}

-- 存储玩家数据接收状态
local playerDataStatus: {[number]: DataStatus} = {}

-- 数据就绪通知事件
local dataReadyEvent = Instance.new("BindableEvent")

-- 验证物品数据格式的辅助函数
local function validateItemsArray(items, context)
	if not items or type(items) ~= "table" then
		return false, context .. "物品数组无效"
	end

	for i, item in ipairs(items) do
		if not item.id or type(item.id) ~= "number" then
			return false, context .. "物品 " .. i .. " 缺少有效的id字段"
		end
		if not item.quantity or type(item.quantity) ~= "number" or item.quantity <= 0 then
			return false, context .. "物品 " .. i .. " 缺少有效的quantity字段"
		end
		if not item.itemType or type(item.itemType) ~= "number" then
			return false, context .. "物品 " .. i .. " 缺少有效的itemType字段"
		end
	end

	return true, ""
end

-- 数据验证函数（支持新旧格式）
local function validateTeleportData(data)
	if not data then
		return false, "数据为空"
	end

	-- 检查是否为新格式（包含playerItemData）
	if data.playerItemData then
		print("🔍 验证新格式传输数据")

		if type(data.playerItemData) ~= "table" then
			return false, "playerItemData字段类型无效"
		end

		-- 验证每个玩家的数据
		for userId, playerData in pairs(data.playerItemData) do
			if type(playerData) ~= "table" then
				return false, "玩家 " .. userId .. " 的数据格式无效"
			end

			if not playerData.playerName or type(playerData.playerName) ~= "string" then
				return false, "玩家 " .. userId .. " 缺少有效的playerName字段"
			end

			if not playerData.occupationId or type(playerData.occupationId) ~= "number" then
				return false, "玩家 " .. userId .. " 缺少有效的occupationId字段"
			end

			-- 验证物品数据
			local isValid, errorMsg = validateItemsArray(playerData.items, "玩家 " .. userId .. " 的")
			if not isValid then
				return false, errorMsg
			end
		end

		return true, "新格式数据验证通过"
	end

	-- 检查旧格式兼容性
	if data.items then
		print("🔍 验证旧格式传输数据")
		local isValid, errorMsg = validateItemsArray(data.items, "")
		if not isValid then
			return false, "旧格式: " .. errorMsg
		end
		return true, "旧格式数据验证通过"
	end

	return false, "数据格式不符合新旧任何格式要求"
end

-- 获取玩家的传输数据（从内存缓存中获取，数据由客户端提供）
function TeleportDataManager:GetPlayerTeleportData(player: Player): TeleportData?
	if not player then
		warn("GetPlayerTeleportData: player参数为空")
		return nil
	end

	-- 从内存中获取数据（数据由客户端通过RemoteEvent发送）
	local cachedData = playerTeleportData[player.UserId]
	if cachedData then
		print("从缓存获取玩家传输数据: " .. player.Name)
		return cachedData
	end

	print("玩家 " .. player.Name .. " 没有传输数据（等待客户端发送）")
	return nil
end

-- 接收客户端发送的传输数据
function TeleportDataManager:ReceiveTeleportDataFromClient(player: Player, teleportData: TeleportData?): boolean
	if not player then
		warn("ReceiveTeleportDataFromClient: player参数为空")
		return false
	end

	if not teleportData then
		-- 标记为没有传输数据
		playerDataStatus[player.UserId] = "no_data"
		-- 清理可能存在的旧数据
		playerTeleportData[player.UserId] = nil

		-- 触发数据就绪事件
		dataReadyEvent:Fire(player, "no_data")
		print("玩家 " .. player.Name .. " 确认没有传输数据")
		return true
	end

	-- 验证数据格式
	local isValid, errorMsg = validateTeleportData(teleportData)
	if not isValid then
		-- 标记为数据无效，当作没有数据处理
		playerDataStatus[player.UserId] = "no_data"
		dataReadyEvent:Fire(player, "invalid_data")
		warn("玩家 " .. player.Name .. " 的传输数据无效: " .. errorMsg)
		return false
	end

	-- 存储数据到内存
	playerTeleportData[player.UserId] = teleportData
	-- 标记为已接收数据
	playerDataStatus[player.UserId] = "received"

	-- 打印详细信息用于调试
	print("传输数据详情:")

	-- 检查新格式数据
	if teleportData.playerItemData then
		print("📦 新格式数据 (playerItemData):")
		print("  源场景:", teleportData.sourcePlace or "未知")
		print("  时间戳:", teleportData.timestamp and os.date("%Y-%m-%d %H:%M:%S", teleportData.timestamp) or "未知")

		for userId, playerData in pairs(teleportData.playerItemData) do
			print("  玩家 " .. userId .. " (" .. playerData.playerName .. "):")
			print("    职业ID:", playerData.occupationId)
			print("    物品数量:", #playerData.items)
			for i, item in ipairs(playerData.items) do
				print(string.format("      %d. ID:%d, 数量:%d, 类型:%d", i, item.id, item.quantity, item.itemType))
			end
		end
	end

	-- 检查旧格式数据
	if teleportData.items then
		print("📦 旧格式数据 (items):")
		print("  职业: " .. (teleportData.job or "未知"))
		print("  描述: " .. (teleportData.str or "未知"))
		print("  物品列表:")
		for i, item in ipairs(teleportData.items) do
			print(string.format("    %d. ID:%d, 数量:%d, 类型:%d", i, item.id, item.quantity, item.itemType))
		end
	end

	-- 触发数据就绪事件
	dataReadyEvent:Fire(player, "received")
	print("玩家 " .. player.Name .. " 传输数据已就绪")

	return true
end

-- 获取玩家的物品数据（兼容新手盒子格式，支持新旧数据格式）
function TeleportDataManager:GetPlayerItems(player: Player)
	local teleportData = self:GetPlayerTeleportData(player)

	if not teleportData then
		return nil
	end

	-- 优先检查新格式数据
	if teleportData.playerItemData then
		local userId = tostring(player.UserId)
		print("🔍 从新格式playerItemData获取玩家物品，玩家ID:", userId)

		for playerId, playerData in pairs(teleportData.playerItemData) do
			if playerId == userId then
				print("✅ 找到玩家专属物品数据，职业ID:", playerData.occupationId, "物品数量:", #playerData.items)
				return playerData.items
			end
		end

		print("⚠️ 在playerItemData中未找到玩家", userId, "的数据")
	end

	-- 回退到旧格式兼容
	if teleportData.items then
		print("📦 使用旧格式items数据，物品数量:", #teleportData.items)
		return teleportData.items
	end

	print("❌ 未找到任何物品数据")
	return nil
end

-- 检查玩家是否有传输数据
function TeleportDataManager:HasPlayerTeleportData(player: Player): boolean
	local teleportData = self:GetPlayerTeleportData(player)
	return teleportData ~= nil
end

-- 获取玩家的职业信息（支持新旧格式）
function TeleportDataManager:GetPlayerJob(player: Player): (string?, string?)
	local teleportData = self:GetPlayerTeleportData(player)

	if not teleportData then
		return nil, nil
	end

	-- 优先检查新格式数据
	if teleportData.playerItemData then
		local userId = tostring(player.UserId)
		for playerId, playerData in pairs(teleportData.playerItemData) do
			if playerId == userId then
				-- 从职业ID转换为职业名称（这里可能需要配置映射）
				local occupationId = playerData.occupationId
				return "职业ID:" .. occupationId, playerData.playerName
			end
		end
	end

	-- 回退到旧格式
	if teleportData.job then
		return teleportData.job, teleportData.str
	end

	return nil, nil
end

-- 新增：获取玩家的职业ID（新格式专用）
function TeleportDataManager:GetPlayerOccupationId(player: Player): number?
	local teleportData = self:GetPlayerTeleportData(player)

	if teleportData and teleportData.playerItemData then
		local userId = tostring(player.UserId)
		for playerId, playerData in pairs(teleportData.playerItemData) do
			if playerId == userId then
				return playerData.occupationId
			end
		end
	end

	return nil
end

-- 清理玩家数据
function TeleportDataManager:CleanupPlayerData(player: Player)
	if not player then return end

	playerTeleportData[player.UserId] = nil
	playerDataStatus[player.UserId] = nil
	print("已清理玩家传输数据: " .. player.Name)
end

-- 初始化管理器
function TeleportDataManager:Initialize()
	print("TeleportDataManager 开始初始化")

	-- 创建或获取RemoteEvent
	local ReplicatedStorage = game:GetService("ReplicatedStorage")
	local remotesFolder = ReplicatedStorage:FindFirstChild("Remotes")
	if not remotesFolder then
		remotesFolder = Instance.new("Folder")
		remotesFolder.Name = "Remotes"
		remotesFolder.Parent = ReplicatedStorage
	end

	local teleportDataRemote = remotesFolder:FindFirstChild("TeleportDataReceived")
	if not teleportDataRemote then
		teleportDataRemote = Instance.new("RemoteEvent")
		teleportDataRemote.Name = "TeleportDataReceived"
		teleportDataRemote.Parent = remotesFolder
	end

	-- 监听客户端发送的传输数据
	teleportDataRemote.OnServerEvent:Connect(function(player: Player, teleportData: TeleportData?)

		self:ReceiveTeleportDataFromClient(player, teleportData)
	end)

	-- 监听玩家离开事件，清理数据
	Players.PlayerRemoving:Connect(function(player: Player)
		self:CleanupPlayerData(player)
	end)

	print("TeleportDataManager 初始化完成，RemoteEvent已设置")
end

-- 调试函数：手动设置玩家传输数据（用于测试）
function TeleportDataManager:SetTestTeleportData(player: Player, testData: TeleportData): boolean
	if not player or not testData then
		warn("SetTestTeleportData: 缺少必要参数")
		return false
	end

	local isValid, errorMsg = validateTeleportData(testData)
	if not isValid then
		warn("测试数据格式无效: " .. errorMsg)
		return false
	end

	playerTeleportData[player.UserId] = testData

	return true
end

-- 获取所有有传输数据的玩家
function TeleportDataManager:GetPlayersWithTeleportData()
	local players = {}
	for userId, data in pairs(playerTeleportData) do
		local player = Players:GetPlayerByUserId(userId)
		if player then
			table.insert(players, {
				player = player,
				data = data
			})
		end
	end
	return players
end

-- 新增：等待玩家传输数据就绪
function TeleportDataManager:WaitForPlayerDataReady(player: Player, timeoutSeconds: number?): DataStatus
	timeoutSeconds = timeoutSeconds or 8 -- 默认8秒超时

	-- 检查是否已经有数据状态
	local currentStatus = playerDataStatus[player.UserId]
	if currentStatus == "received" or currentStatus == "no_data" then
		print("玩家 " .. player.Name .. " 数据已就绪，状态: " .. currentStatus)
		return currentStatus
	end

	-- 标记为等待状态
	playerDataStatus[player.UserId] = "waiting"
	print("开始等待玩家 " .. player.Name .. " 的传输数据，超时时间: " .. timeoutSeconds .. "秒")

	-- 创建超时计时器
	local timeoutReached = false
	spawn(function()
		wait(timeoutSeconds)
		if playerDataStatus[player.UserId] == "waiting" then
			playerDataStatus[player.UserId] = "timeout"
			timeoutReached = true
			dataReadyEvent:Fire(player, "timeout")
			print("玩家 " .. player.Name .. " 传输数据等待超时")
		end
	end)

	-- 等待数据就绪事件
	local connection
	local dataStatus: DataStatus? = nil

	connection = dataReadyEvent.Event:Connect(function(eventPlayer: Player, status: DataStatus)
		if eventPlayer == player then
			dataStatus = status
			connection:Disconnect()
		end
	end)

	-- 等待直到有结果
	while not dataStatus and not timeoutReached do
		wait(0.1)
	end

	-- 清理连接
	if connection then
		connection:Disconnect()
	end

	local finalStatus = playerDataStatus[player.UserId] or "timeout"
	print("玩家 " .. player.Name .. " 数据等待完成，最终状态: " .. finalStatus)
	return finalStatus
end

-- 新增：获取玩家数据状态
function TeleportDataManager:GetPlayerDataStatus(player: Player): DataStatus
	return playerDataStatus[player.UserId] or "unknown"
end

-- 新增：手动设置玩家为等待状态（用于预处理）
function TeleportDataManager:SetPlayerWaiting(player: Player)
	playerDataStatus[player.UserId] = "waiting"
	print("设置玩家 " .. player.Name .. " 为等待传输数据状态")
end

return TeleportDataManager
