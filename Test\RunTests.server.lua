--!strict
-- 统一测试运行脚本
-- 在服务器端运行所有可用的测试

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 等待所有必要的服务加载
wait(2)

print("🧪 开始运行完整测试套件...")

-- 可用的测试模块
local testModules = {
	TeleportDataTest = require(ReplicatedStorage.Scripts.Test.TeleportDataTest),
	TeleportFixTest = require(ReplicatedStorage.Scripts.Test.TeleportFixTest),
	GameOverTeleportFlowTest = require(ReplicatedStorage.Scripts.Test.GameOverTeleportFlowTest),
	PlayerDownedFlowTest = require(ReplicatedStorage.Scripts.Test.PlayerDownedFlowTest),
	MultiPlayerDownedFlowTest = require(ReplicatedStorage.Scripts.Test.MultiPlayerDownedFlowTest)
}

-- 运行所有测试
spawn(function()
	wait(1)
	print("\n" .. "="*60)
	print("🔬 完整测试套件运行")
	print("="*60)

	-- 1. 传送数据系统测试
	print("\n📋 1. 传送数据系统测试")
	print("-"*40)
	testModules.TeleportDataTest.runAllTests()
	testModules.TeleportDataTest.testDataValidation()

	-- 2. 传送修复验证测试
	print("\n📋 2. 传送修复验证测试")
	print("-"*40)
	testModules.TeleportFixTest.runCompleteFixTest()

	-- 3. 游戏结束传送流程测试
	print("\n📋 3. 游戏结束传送流程测试")
	print("-"*40)
	testModules.GameOverTeleportFlowTest.runCompleteFlowTest()

	-- 4. 濒死流程测试
	print("\n📋 4. 濒死流程测试")
	print("-"*40)
	testModules.PlayerDownedFlowTest.runCompleteTest()

	-- 5. 多人游戏濒死流程测试
	print("\n📋 5. 多人游戏濒死流程测试")
	print("-"*40)
	testModules.MultiPlayerDownedFlowTest.verifyFix()

	print("\n" .. "="*60)
	print("🏁 完整测试套件运行完成")
	print("="*60 .. "\n")

	-- 显示测试帮助信息
	print("💡 可用的单独测试命令:")
	print("  TeleportDataTest.runQuickTest()")
	print("  TeleportFixTest.runQuickTeleportTest()")
	print("  GameOverTeleportFlowTest.runQuickTest()")
	print("  PlayerDownedFlowTest.runQuickTest()")
	print("  MultiPlayerDownedFlowTest.runQuickTest()")
end)
