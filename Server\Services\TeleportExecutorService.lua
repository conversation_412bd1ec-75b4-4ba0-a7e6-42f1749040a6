--!strict
-- 传送执行服务
-- 负责在服务端执行传送操作

local TeleportService = game:GetService("TeleportService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)
local TeleportConfig = require(ReplicatedStorage.Scripts.Config.TeleportConfig)

local TeleportExecutorService = {}

-- 初始化服务
function TeleportExecutorService:Initialize()
	print("TeleportExecutorService 初始化开始")
	
	-- 注册传送请求事件
	NotifyService.RegisterServerEvent("RequestTeleportToInitial", function(player, data)
		self:HandleTeleportRequest(player, data)
	end)
	
	print("TeleportExecutorService 初始化完成")
end

-- 处理传送请求
function TeleportExecutorService:HandleTeleportRequest(player, data)
	print("🚀 收到传送请求，玩家:", player.Name, "数据:", data)
	
	-- 验证请求数据
	if not data or not data.targetPlaceId then
		warn("❌ 传送请求数据无效:", data)
		self:NotifyTeleportFailed(player, "请求数据无效")
		return
	end
	
	local targetPlaceId = data.targetPlaceId
	local reason = data.reason or "unknown"
	
	print("传送目标场景ID:", targetPlaceId)
	print("传送原因:", reason)
	
	-- 执行传送
	self:ExecuteTeleportToPlace(player, targetPlaceId, reason)
end

-- 执行传送到指定场景
function TeleportExecutorService:ExecuteTeleportToPlace(player, targetPlaceId, reason)
	print("🚀 执行传送，玩家:", player.Name, "目标场景:", targetPlaceId)
	
	-- 验证玩家
	if not player or not player.Parent then
		warn("❌ 玩家无效或已离开游戏")
		return
	end
	
	-- 验证场景ID
	if not targetPlaceId or type(targetPlaceId) ~= "number" or targetPlaceId <= 0 then
		warn("❌ 无效的场景ID:", targetPlaceId)
		self:NotifyTeleportFailed(player, "无效的场景ID")
		return
	end
	
	-- 通知客户端传送开始
	NotifyService.FireClient(player, "TeleportToInitialStart", {
		targetPlaceId = targetPlaceId,
		reason = reason
	})
	
	-- 准备传送数据
	local teleportOptions = Instance.new("TeleportOptions")
	teleportOptions:SetTeleportData({
		sourcePlace = game.PlaceId,
		timestamp = os.time(),
		returnReason = reason,
		returnTime = os.date("%Y-%m-%d %H:%M:%S", os.time()),
		playerName = player.Name,
		playerId = player.UserId
	})
	
	-- 执行传送
	local success, errorMessage = pcall(function()
		TeleportService:TeleportAsync(targetPlaceId, {player}, teleportOptions)
	end)
	
	if success then
		print("✅ 传送成功，玩家:", player.Name, "已传送到场景:", targetPlaceId)
	else
		warn("❌ 传送失败，玩家:", player.Name, "错误:", errorMessage)
		self:NotifyTeleportFailed(player, errorMessage)
	end
end

-- 通知传送失败
function TeleportExecutorService:NotifyTeleportFailed(player, errorMessage)
	print("📢 通知传送失败，玩家:", player.Name, "错误:", errorMessage)
	
	-- 通知客户端传送失败
	NotifyService.FireClient(player, "TeleportToInitialFailed", {
		error = errorMessage,
		timestamp = os.time()
	})
end

-- 传送所有玩家到初始场景
function TeleportExecutorService:TeleportAllPlayersToInitial(reason)
	print("🚀 传送所有玩家到初始场景，原因:", reason or "unknown")
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers == 0 then
		warn("❌ 没有玩家需要传送")
		return
	end
	
	local targetPlaceId = TeleportConfig:GetInitialPlaceId()
	print("目标场景ID:", targetPlaceId, "玩家数量:", #allPlayers)
	
	-- 通知所有客户端传送开始
	NotifyService.FireAllClient("TeleportToInitialStart", {
		targetPlaceId = targetPlaceId,
		reason = reason,
		playerCount = #allPlayers
	})
	
	-- 准备传送数据
	local teleportOptions = Instance.new("TeleportOptions")
	teleportOptions:SetTeleportData({
		sourcePlace = game.PlaceId,
		timestamp = os.time(),
		returnReason = reason or "allPlayersTeleport",
		returnTime = os.date("%Y-%m-%d %H:%M:%S", os.time()),
		playerCount = #allPlayers
	})
	
	-- 执行传送
	local success, errorMessage = pcall(function()
		TeleportService:TeleportAsync(targetPlaceId, allPlayers, teleportOptions)
	end)
	
	if success then
		print("✅ 所有玩家传送成功，已传送", #allPlayers, "个玩家到场景:", targetPlaceId)
	else
		warn("❌ 所有玩家传送失败，错误:", errorMessage)
		
		-- 通知所有客户端传送失败
		NotifyService.FireAllClient("TeleportToInitialFailed", {
			error = errorMessage,
			timestamp = os.time(),
			playerCount = #allPlayers
		})
	end
end

-- 手动传送玩家（用于测试或管理员命令）
function TeleportExecutorService:ManualTeleportPlayer(player, targetPlaceId, reason)
	print("🔧 手动传送玩家:", player.Name, "到场景:", targetPlaceId)
	
	self:ExecuteTeleportToPlace(player, targetPlaceId, reason or "manual")
end

-- 手动传送所有玩家（用于测试或管理员命令）
function TeleportExecutorService:ManualTeleportAllPlayers(reason)
	print("🔧 手动传送所有玩家")
	
	self:TeleportAllPlayersToInitial(reason or "manual")
end

-- 获取传送统计信息
function TeleportExecutorService:GetTeleportStats()
	local allPlayers = Players:GetPlayers()
	local targetPlaceId = TeleportConfig:GetInitialPlaceId()
	
	return {
		currentPlaceId = game.PlaceId,
		targetPlaceId = targetPlaceId,
		playerCount = #allPlayers,
		timestamp = os.time()
	}
end

return TeleportExecutorService
